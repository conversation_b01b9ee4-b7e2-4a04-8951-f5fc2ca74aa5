# AWS Automation Project .gitignore
# Author: <PERSON><PERSON>
# Date: 2025-10-07

# =============================================================================
# APPLICATION FILES - NEVER COMMIT THESE
# =============================================================================

# Windows Application Installers
S3/windows/applications/**/*.msi
S3/windows/applications/**/*.exe
S3/windows/applications/**/*.zip
S3/windows/applications/**/*.rar
S3/windows/applications/**/*.7z
S3/windows/applications/**/*.tar.gz
S3/windows/applications/**/*.deb
S3/windows/applications/**/*.rpm
S3/windows/applications/**/*.dmg
S3/windows/applications/**/*.pkg
S3/windows/applications/**/*.iso

# HashiCorp Vault Executables
S3/windows/applications/vault/vault.exe
S3/windows/applications/vault/vault_*.zip
S3/windows/applications/vault/vault-version.txt

# Specific Application Directories (keep structure, ignore binaries)
S3/windows/applications/*/
!S3/windows/applications/*/.gitkeep
!S3/windows/applications/*/README.md

# Large binary files that might be accidentally added
**/*.msi
**/*.exe
**/*.zip
**/*.rar
**/*.7z
**/*.tar.gz
**/*.iso
**/*.dmg
**/*.pkg

# Exclude from binary ignore (allow specific scripts and tools)
!**/scripts/**/*.exe
!**/tools/**/*.exe
!**/bin/**/*.exe

# =============================================================================
# SENSITIVE CONFIGURATION FILES
# =============================================================================

# AWS Credentials and Config
.aws/
aws-credentials.json
**/aws-credentials.*
**/credentials.*
**/*credentials*
**/*secrets*

# Terraform State and Variables
**/*.tfstate
**/*.tfstate.*
**/*.tfvars
**/*.tfvars.json
terraform.tfstate*
.terraform/
.terraform.lock.hcl

# Environment Variables and Secrets
.env
.env.*
**/*.env
**/*.secrets
secrets.json
vault-tokens.*
**/*token*
**/*password*
**/*secret*

# SSH Keys and Certificates
**/*.pem
**/*.key
**/*.crt
**/*.cer
**/*.p12
**/*.pfx
id_rsa*
id_ed25519*
known_hosts

# =============================================================================
# TEMPORARY AND CACHE FILES
# =============================================================================

# PowerShell
**/*.ps1xml
**/Microsoft.PowerShell_profile.ps1

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
venv/
env/
ENV/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Logs
logs/
*.log
**/*.log
log/
**/*debug.log
**/*error.log
**/*access.log

# Temporary files
tmp/
temp/
*.tmp
*.temp
**/*.tmp
**/*.temp
.cache/
cache/

# =============================================================================
# DEVELOPMENT AND IDE FILES
# =============================================================================

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Visual Studio
.vs/
*.suo
*.user
*.userosscache
*.sln.docstates
bin/
obj/

# JetBrains IDEs
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# OPERATING SYSTEM FILES
# =============================================================================

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# AWS AND CLOUD SPECIFIC
# =============================================================================

# CloudFormation
**/*-stack-outputs.json
**/*-stack-parameters.json
**/stack-outputs/
**/cfn-response/

# CDK
cdk.out/
.cdk.staging/
cdk.context.json

# SAM
.aws-sam/
samconfig.toml

# Serverless Framework
.serverless/

# =============================================================================
# BACKUP AND ARCHIVE FILES
# =============================================================================

# Backup files
*.bak
*.backup
*.old
*.orig
**/*.bak
**/*.backup
**/*.old
**/*.orig

# Archive files (unless specifically needed)
*.tar
*.gz
*.bz2
*.xz

# =============================================================================
# TEST AND BUILD ARTIFACTS
# =============================================================================

# Test results
test-results/
coverage/
*.coverage
.nyc_output/
.pytest_cache/

# Build artifacts
build/
dist/
out/
target/

# =============================================================================
# DOCUMENTATION BUILDS
# =============================================================================

# Sphinx documentation
docs/_build/
docs/build/

# MkDocs
site/

# =============================================================================
# CUSTOM PROJECT EXCLUSIONS
# =============================================================================

# Local development overrides
local/
local-*
dev-*
*-local.*

# Personal notes and scratch files
notes/
scratch/
TODO.md
NOTES.md
*.notes
*.scratch

# Downloaded files during development
downloads/
**/downloads/

# Test data and mock files
test-data/
mock-data/
sample-data/

# =============================================================================
# KEEP THESE FILES (OVERRIDE ABOVE RULES)
# =============================================================================

# Important documentation
!README.md
!**/README.md
!CHANGELOG.md
!LICENSE
!.gitkeep

# Configuration templates and examples
!**/*template*
!**/*example*
!**/*sample*
!**/example-*
!**/template-*
!**/sample-*

# Scripts and automation (but not binaries)
!**/*.ps1
!**/*.py
!**/*.sh
!**/*.bat
!**/*.cmd

# Infrastructure as Code
!**/*.yml
!**/*.yaml
!**/*.json
!**/*.tf
!**/*.hcl

# =============================================================================
# SPECIAL CASES FOR THIS PROJECT
# =============================================================================

# Keep application directory structure but ignore contents
S3/windows/applications/*/
!S3/windows/applications/*/.gitkeep
!S3/windows/applications/*/README.md
!S3/windows/applications/*/install-instructions.txt

# Keep Qualys directory structure (example)
!S3/windows/applications/qualys/.gitkeep
!S3/windows/applications/qualys/README.md

# Allow small configuration files in applications directory
!S3/windows/applications/**/*.json
!S3/windows/applications/**/*.xml
!S3/windows/applications/**/*.cfg
!S3/windows/applications/**/*.conf
!S3/windows/applications/**/*.ini

# Terraform specific for this project
!Terraform/*.tf
!Terraform/*.hcl
!Terraform/*.yaml
!Terraform/*.yml

# Keep ImageBuilder components
!ImageBuilder/components/*.yml
!ImageBuilder/components/*.yaml

# Keep SSM documents
!SystemsManager/**/*.yml
!SystemsManager/**/*.yaml
!SystemsManager/**/*.json