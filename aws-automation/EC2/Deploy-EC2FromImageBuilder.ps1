<#
    .NOTES
    File Name: Deploy-EC2FromImageBuilder.ps1
    Author: <PERSON><PERSON>
    Version: 1.0.0 - 03/09/2025
    Description: Deploys EC2 instances from ImageBuilder recipes with proper configuration
    Note: This script is designed to be called from SSM Automation runbooks

    .NOTES
    Version 1.0.0     - Base Script
#>

param(
    [Parameter(Mandatory=$true)]
    [string]$ImageBuilderImageArn,
    
    [Parameter(Mandatory=$true)]
    [string]$InstanceType,
    
    [Parameter(Mandatory=$true)]
    [string]$SubnetId,
    
    [Parameter(Mandatory=$true)]
    [string]$SecurityGroupIds,
    
    [Parameter(Mandatory=$true)]
    [string]$KeyName,
    
    [Parameter(Mandatory=$true)]
    [hashtable]$DeploymentConfig,
    
    [Parameter(Mandatory=$false)]
    [string]$ComputerName = "",
    
    [Parameter(Mandatory=$false)]
    [string]$UserDataScript = "",
    
    [Parameter(Mandatory=$false)]
    [string]$IamInstanceProfile = "",
    
    [Parameter(Mandatory=$false)]
    [string]$Region = "af-south-1",
    
    [Parameter(Mandatory=$false)]
    [switch]$WaitForRunning = $true,
    
    [Parameter(Mandatory=$false)]
    [int]$WaitTimeoutMinutes = 10,
    
    [Parameter(Mandatory=$false)]
    [switch]$OutputJson = $false
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to write structured log messages
function Write-LogMessage {
    param(
        [string]$Message,
        [ValidateSet("INFO", "WARN", "ERROR", "SUCCESS")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "INFO" { Write-Host $logMessage -ForegroundColor Cyan }
        "WARN" { Write-Host $logMessage -ForegroundColor Yellow }
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
    }
}

# Function to get latest AMI from ImageBuilder image
function Get-ImageBuilderAMI {
    param(
        [string]$ImageArn,
        [string]$Region
    )
    
    try {
        Write-LogMessage "Getting latest AMI from ImageBuilder image: $ImageArn" "INFO"
        
        # Get image details
        $imageDetails = aws imagebuilder get-image --image-build-version-arn $ImageArn --region $Region --output json | ConvertFrom-Json
        
        if (-not $imageDetails -or -not $imageDetails.image) {
            throw "Failed to get ImageBuilder image details"
        }
        
        # Extract AMI ID from output resources
        $amiId = $null
        foreach ($resource in $imageDetails.image.outputResources.amis) {
            if ($resource.region -eq $Region) {
                $amiId = $resource.image
                break
            }
        }
        
        if (-not $amiId) {
            throw "No AMI found for region $Region in ImageBuilder image"
        }
        
        Write-LogMessage "Found AMI: $amiId" "SUCCESS"
        return $amiId
        
    } catch {
        Write-LogMessage "Failed to get ImageBuilder AMI: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to generate instance tags
function New-InstanceTags {
    param(
        [hashtable]$Config,
        [string]$ComputerName
    )
    
    try {
        Write-LogMessage "Generating instance tags" "INFO"
        
        $instanceName = if ($ComputerName) { $ComputerName } else { "$($Config.AssetOwner)-$($Config.Client)-$($Config.AppType)-$(Get-Date -Format 'yyyyMMdd-HHmm')" }
        
        $tags = @(
            @{ Key = "Name"; Value = $instanceName },
            @{ Key = "AssetOwner"; Value = $Config.AssetOwner },
            @{ Key = "AppType"; Value = $Config.AppType },
            @{ Key = "Client"; Value = $Config.Client },
            @{ Key = "Environment"; Value = $Config.Environment },
            @{ Key = "OSVersion"; Value = $Config.OSVersion },
            @{ Key = "Domain"; Value = $Config.Domain },
            @{ Key = "TargetOU"; Value = $Config.TargetOU },
            @{ Key = "AutoDomainJoin"; Value = "true" },
            @{ Key = "CreatedBy"; Value = "SSM-Automation" },
            @{ Key = "CreatedDate"; Value = (Get-Date -Format "yyyy-MM-dd") },
            @{ Key = "DeploymentMethod"; Value = "ImageBuilder-SSM" }
        )
        
        Write-LogMessage "Generated $($tags.Count) instance tags" "SUCCESS"
        return $tags
        
    } catch {
        Write-LogMessage "Failed to generate instance tags: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to encode user data
function ConvertTo-Base64UserData {
    param(
        [string]$UserDataContent
    )
    
    try {
        if (-not $UserDataContent) {
            return ""
        }
        
        Write-LogMessage "Encoding user data script" "INFO"
        
        $userDataBytes = [System.Text.Encoding]::UTF8.GetBytes($UserDataContent)
        $userDataBase64 = [System.Convert]::ToBase64String($userDataBytes)
        
        Write-LogMessage "User data encoded successfully" "SUCCESS"
        return $userDataBase64
        
    } catch {
        Write-LogMessage "Failed to encode user data: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to launch EC2 instance
function New-EC2InstanceFromAMI {
    param(
        [string]$AMIId,
        [string]$InstanceType,
        [string]$SubnetId,
        [string]$SecurityGroupIds,
        [string]$KeyName,
        [array]$Tags,
        [string]$UserDataBase64,
        [string]$IamInstanceProfile,
        [string]$Region
    )
    
    try {
        Write-LogMessage "Launching EC2 instance from AMI: $AMIId" "INFO"
        
        # Build launch parameters
        $launchParams = @(
            "--image-id", $AMIId,
            "--count", "1",
            "--instance-type", $InstanceType,
            "--key-name", $KeyName,
            "--security-group-ids", $SecurityGroupIds,
            "--subnet-id", $SubnetId,
            "--region", $Region,
            "--output", "json"
        )
        
        # Add IAM instance profile if provided
        if ($IamInstanceProfile) {
            $launchParams += @("--iam-instance-profile", "Name=$IamInstanceProfile")
        }
        
        # Add user data if provided
        if ($UserDataBase64) {
            $launchParams += @("--user-data", $UserDataBase64)
        }
        
        # Add tag specifications
        if ($Tags -and $Tags.Count -gt 0) {
            $tagSpec = @{
                ResourceType = "instance"
                Tags = $Tags
            }
            $tagSpecJson = ConvertTo-Json @($tagSpec) -Depth 10 -Compress
            $launchParams += @("--tag-specifications", $tagSpecJson)
        }
        
        # Launch instance
        Write-LogMessage "Executing EC2 run-instances command" "INFO"
        $launchResult = & aws ec2 run-instances @launchParams
        
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to launch EC2 instance: $launchResult"
        }
        
        $instanceData = $launchResult | ConvertFrom-Json
        $instance = $instanceData.Instances[0]
        
        Write-LogMessage "EC2 instance launched successfully: $($instance.InstanceId)" "SUCCESS"
        return $instance
        
    } catch {
        Write-LogMessage "Failed to launch EC2 instance: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to wait for instance to reach running state
function Wait-ForInstanceRunning {
    param(
        [string]$InstanceId,
        [int]$TimeoutMinutes,
        [string]$Region
    )

    try {
        Write-LogMessage "Waiting for instance $InstanceId to reach running state (timeout: $TimeoutMinutes minutes)" "INFO"

        $timeoutSeconds = $TimeoutMinutes * 60
        $startTime = Get-Date

        do {
            Start-Sleep -Seconds 30

            $instanceState = aws ec2 describe-instances --instance-ids $InstanceId --region $Region --output json | ConvertFrom-Json
            $currentState = $instanceState.Reservations[0].Instances[0].State.Name

            Write-LogMessage "Current instance state: $currentState" "INFO"

            if ($currentState -eq "running") {
                Write-LogMessage "Instance is now running" "SUCCESS"
                return $true
            }

            if ($currentState -eq "terminated" -or $currentState -eq "stopping" -or $currentState -eq "stopped") {
                throw "Instance entered unexpected state: $currentState"
            }

            $elapsedSeconds = ((Get-Date) - $startTime).TotalSeconds

        } while ($elapsedSeconds -lt $timeoutSeconds)

        throw "Timeout waiting for instance to reach running state after $TimeoutMinutes minutes"

    } catch {
        Write-LogMessage "Failed while waiting for instance: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Main execution logic
try {
    Write-LogMessage "Starting EC2 Instance Deployment from ImageBuilder" "INFO"
    Write-LogMessage "ImageBuilder ARN: $ImageBuilderImageArn" "INFO"
    Write-LogMessage "Instance Type: $InstanceType, Subnet: $SubnetId" "INFO"

    # Step 1: Get AMI from ImageBuilder image
    $amiId = Get-ImageBuilderAMI -ImageArn $ImageBuilderImageArn -Region $Region

    # Step 2: Generate instance tags
    $instanceTags = New-InstanceTags -Config $DeploymentConfig -ComputerName $ComputerName

    # Step 3: Encode user data if provided
    $userDataBase64 = ConvertTo-Base64UserData -UserDataContent $UserDataScript

    # Step 4: Launch EC2 instance
    $instance = New-EC2InstanceFromAMI -AMIId $amiId -InstanceType $InstanceType -SubnetId $SubnetId -SecurityGroupIds $SecurityGroupIds -KeyName $KeyName -Tags $instanceTags -UserDataBase64 $userDataBase64 -IamInstanceProfile $IamInstanceProfile -Region $Region

    # Step 5: Wait for instance to be running if requested
    if ($WaitForRunning) {
        Wait-ForInstanceRunning -InstanceId $instance.InstanceId -TimeoutMinutes $WaitTimeoutMinutes -Region $Region

        # Get updated instance information
        $instanceDetails = aws ec2 describe-instances --instance-ids $instance.InstanceId --region $Region --output json | ConvertFrom-Json
        $updatedInstance = $instanceDetails.Reservations[0].Instances[0]
    } else {
        $updatedInstance = $instance
    }

    # Step 6: Build final result object
    $finalResult = @{
        Success = $true
        InstanceId = $instance.InstanceId
        AMIId = $amiId
        InstanceType = $InstanceType
        State = $updatedInstance.State.Name
        PrivateIpAddress = $updatedInstance.PrivateIpAddress
        PublicIpAddress = $updatedInstance.PublicIpAddress
        SubnetId = $SubnetId
        SecurityGroups = $SecurityGroupIds
        KeyName = $KeyName
        LaunchTime = $instance.LaunchTime
        ImageBuilderArn = $ImageBuilderImageArn
        Region = $Region

        # Configuration details
        DeploymentConfig = $DeploymentConfig
        ComputerName = if ($ComputerName) { $ComputerName } else { "Auto-generated" }

        # Tags applied
        Tags = $instanceTags

        # Metadata
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
        WaitedForRunning = $WaitForRunning
    }

    # Add IAM instance profile if used
    if ($IamInstanceProfile) {
        $finalResult.IamInstanceProfile = $IamInstanceProfile
    }

    # Output results
    if ($OutputJson) {
        Write-Output ($finalResult | ConvertTo-Json -Depth 10)
    } else {
        Write-LogMessage "=== EC2 INSTANCE DEPLOYMENT COMPLETED ===" "SUCCESS"
        Write-LogMessage "Instance ID: $($instance.InstanceId)" "INFO"
        Write-LogMessage "AMI ID: $amiId" "INFO"
        Write-LogMessage "Instance Type: $InstanceType" "INFO"
        Write-LogMessage "State: $($updatedInstance.State.Name)" "INFO"
        Write-LogMessage "Private IP: $($updatedInstance.PrivateIpAddress)" "INFO"
        if ($updatedInstance.PublicIpAddress) {
            Write-LogMessage "Public IP: $($updatedInstance.PublicIpAddress)" "INFO"
        }
        Write-LogMessage "Launch Time: $($instance.LaunchTime)" "INFO"
    }

    # Return result for use in runbook
    return $finalResult

} catch {
    Write-LogMessage "EC2 Instance deployment failed: $($_.Exception.Message)" "ERROR"

    $errorResult = @{
        Success = $false
        Error = $true
        Message = $_.Exception.Message
        ImageBuilderArn = $ImageBuilderImageArn
        InstanceType = $InstanceType
        SubnetId = $SubnetId
        SecurityGroups = $SecurityGroupIds
        ScriptName = $MyInvocation.MyCommand.Name
        LineNumber = $_.InvocationInfo.ScriptLineNumber
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
    }

    if ($OutputJson) {
        Write-Output ($errorResult | ConvertTo-Json -Depth 5)
    }

    exit 1
}
