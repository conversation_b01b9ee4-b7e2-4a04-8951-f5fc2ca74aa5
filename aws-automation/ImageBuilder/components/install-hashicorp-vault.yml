# AWS ImageBuilder Component: Install HashiCorp Vault
# Author: <PERSON><PERSON>
# Date: 2025-10-07
# Description: Download and install latest HashiCorp Vault from GitHub releases

name: install-hashicorp-vault
description: Download and install latest HashiCorp Vault executable from GitHub releases
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: InstallHashiCorpVault
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              <#
                  .SYNOPSIS
                  Downloads and installs the latest HashiCorp Vault executable
                  
                  .DESCRIPTION
                  This script downloads the latest HashiCorp Vault release from GitHub,
                  installs it to a dedicated directory, and configures PATH access.
                  
                  .NOTES
                  Author: R<PERSON> van Z<PERSON>
                  Version: 1.0.0
                  Date: 2025-10-07
              #>
              
              # Configuration
              $installDir = "C:\Program Files\HashiCorp\Vault"
              $tempDir = "C:\Temp\VaultInstall"
              $githubRepo = "hashicorp/vault"
              $executableName = "vault.exe"
              
              # Function to write structured log messages
              function Write-Log {
                  param(
                      [string]$Message,
                      [ValidateSet("INFO", "WARN", "ERROR", "SUCCESS")]
                      [string]$Level = "INFO"
                  )
                  
                  $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
                  $logMessage = "[$timestamp] [$Level] $Message"
                  
                  switch ($Level) {
                      "INFO" { Write-Host $logMessage -ForegroundColor Cyan }
                      "WARN" { Write-Host $logMessage -ForegroundColor Yellow }
                      "ERROR" { Write-Host $logMessage -ForegroundColor Red }
                      "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
                  }
              }
              
              # Function to create directory if it doesn't exist
              function New-Directory {
                  param([string]$Path)
                  
                  if (!(Test-Path $Path)) {
                      New-Item -ItemType Directory -Path $Path -Force | Out-Null
                      Write-Log "Created directory: $Path" "SUCCESS"
                  } else {
                      Write-Log "Directory already exists: $Path" "INFO"
                  }
              }
              
              try {
                  Write-Log "Starting HashiCorp Vault installation process" "INFO"
                  
                  # Create directories
                  New-Directory -Path $installDir
                  New-Directory -Path $tempDir
                  
                  # Get latest release information from GitHub API
                  Write-Log "Fetching latest Vault release information from GitHub..." "INFO"
                  $releaseUrl = "https://api.github.com/repos/$githubRepo/releases/latest"
                  
                  try {
                      $releaseInfo = Invoke-RestMethod -Uri $releaseUrl -UseBasicParsing -TimeoutSec 30
                      Write-Log "Latest Vault version: $($releaseInfo.tag_name)" "SUCCESS"
                      
                      # Find Windows x64 ZIP asset
                      $asset = $releaseInfo.assets | Where-Object { 
                          $_.name -like "*windows_amd64.zip" -or 
                          $_.name -like "*win64.zip" -or
                          $_.name -like "*windows-amd64.zip"
                      } | Select-Object -First 1
                      
                      if (-not $asset) {
                          throw "Could not find Windows x64 asset in release"
                      }
                      
                      Write-Log "Found asset: $($asset.name)" "SUCCESS"
                      $downloadUrl = $asset.browser_download_url
                      $zipFileName = $asset.name
                      
                  } catch {
                      Write-Log "Failed to get release info from GitHub API: $($_.Exception.Message)" "ERROR"
                      
                      # Fallback to known pattern (may need updating)
                      Write-Log "Using fallback download pattern..." "WARN"
                      $fallbackVersion = "1.15.2"  # Update this periodically
                      $zipFileName = "vault_${fallbackVersion}_windows_amd64.zip"
                      $downloadUrl = "https://releases.hashicorp.com/vault/$fallbackVersion/$zipFileName"
                      Write-Log "Fallback URL: $downloadUrl" "INFO"
                  }
                  
                  # Download the ZIP file
                  $zipPath = Join-Path $tempDir $zipFileName
                  Write-Log "Downloading Vault from: $downloadUrl" "INFO"
                  Write-Log "Download path: $zipPath" "INFO"
                  
                  try {
                      # Use TLS 1.2 for secure downloads
                      [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
                      
                      Invoke-WebRequest -Uri $downloadUrl -OutFile $zipPath -UseBasicParsing -TimeoutSec 300
                      
                      if (Test-Path $zipPath) {
                          $fileSize = (Get-Item $zipPath).Length
                          Write-Log "Download completed successfully. Size: $([math]::Round($fileSize / 1MB, 2)) MB" "SUCCESS"
                      } else {
                          throw "Downloaded file not found at expected location"
                      }
                      
                  } catch {
                      Write-Log "Download failed: $($_.Exception.Message)" "ERROR"
                      throw
                  }
                  
                  # Extract the ZIP file
                  Write-Log "Extracting Vault executable..." "INFO"
                  try {
                      # Use .NET method for reliable extraction
                      Add-Type -AssemblyName System.IO.Compression.FileSystem
                      [System.IO.Compression.ZipFile]::ExtractToDirectory($zipPath, $tempDir)
                      
                      # Find the vault executable
                      $extractedExe = Get-ChildItem -Path $tempDir -Name $executableName -Recurse | Select-Object -First 1
                      
                      if ($extractedExe) {
                          $sourcePath = Join-Path $tempDir $extractedExe
                          $targetPath = Join-Path $installDir $executableName
                          
                          # Copy executable to install directory
                          Copy-Item -Path $sourcePath -Destination $targetPath -Force
                          Write-Log "Vault executable installed to: $targetPath" "SUCCESS"
                          
                          # Verify the installation
                          if (Test-Path $targetPath) {
                              $version = & $targetPath version 2>&1
                              Write-Log "Vault installation verified. Version: $version" "SUCCESS"
                          } else {
                              throw "Vault executable not found at target location"
                          }
                          
                      } else {
                          throw "Vault executable not found in extracted files"
                      }
                      
                  } catch {
                      Write-Log "Extraction failed: $($_.Exception.Message)" "ERROR"
                      throw
                  }
                  
                  # Add to system PATH
                  Write-Log "Adding Vault to system PATH..." "INFO"
                  try {
                      $currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
                      
                      if ($currentPath -notlike "*$installDir*") {
                          $newPath = "$currentPath;$installDir"
                          [Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")
                          Write-Log "Added $installDir to system PATH" "SUCCESS"
                      } else {
                          Write-Log "Vault directory already in system PATH" "INFO"
                      }
                      
                  } catch {
                      Write-Log "Failed to update PATH: $($_.Exception.Message)" "WARN"
                      Write-Log "Vault can still be accessed via full path: $targetPath" "INFO"
                  }
                  
                  # Cleanup temporary files
                  Write-Log "Cleaning up temporary files..." "INFO"
                  try {
                      Remove-Item -Path $tempDir -Recurse -Force -ErrorAction SilentlyContinue
                      Write-Log "Temporary files cleaned up" "SUCCESS"
                  } catch {
                      Write-Log "Failed to cleanup temporary files: $($_.Exception.Message)" "WARN"
                  }
                  
                  Write-Log "HashiCorp Vault installation completed successfully" "SUCCESS"
                  Write-Log "Vault executable location: $targetPath" "INFO"
                  Write-Log "Vault can be accessed globally via 'vault' command after PATH refresh" "INFO"
                  
              } catch {
                  Write-Log "Vault installation failed: $($_.Exception.Message)" "ERROR"
                  exit 1
              }
