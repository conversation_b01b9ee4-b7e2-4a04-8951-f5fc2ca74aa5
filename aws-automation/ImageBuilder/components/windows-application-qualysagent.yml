# AWS ImageBuilder Component: Install Qualys Cloud Agent
# Author: <PERSON><PERSON>
# Date: 2025-010-06
# Description: Install and configure Qualys Cloud Agent for vulnerability scanning and compliance monitoring
# Documentation: https://docs.qualys.com/en/integration/aws-ec2-image-builder/ca_component/create_custom_component.htm

name: windows-application-qualysagent
description: Install Qualys Cloud Agent with customer-specific configuration
schemaVersion: 1.0

parameters:
  - CustomerId:
      type: string
      default: "********-87a7-f49e-8287-1d2bbd165875"
      description: Qualys Customer ID for your subscription
  - ActivationId:
      type: string
      default: "84671cee-cbd9-4ae6-bab7-75ae45ae16b6"
      description: Qualys Activation ID to activate the Cloud Agent
  - WebServiceUri:
      type: string
      default: "https://qagpublic.qg2.apps.qualys.eu/CloudAgent/"
      description: Qualys platform URL where your account is hosted
  - ProxyServer:
      type: string
      default: ""
      description: (Optional) Proxy server IP/hostname for Cloud Agent communication
  - ProxyPort:
      type: string
      default: ""
      description: (Optional) Proxy server port for Cloud Agent communication

phases:
  - name: build
    steps:
      - name: CheckExistingInstallation
        action: ExecutePowerShell
        onFailure: Continue
        inputs:
          commands:
            - |
              Write-Host "Checking for existing Qualys Cloud Agent installation..."

              # Check if Qualys Cloud Agent is already installed
              $qualysService = Get-Service -Name "QualysAgent" -ErrorAction SilentlyContinue

              if ($qualysService) {
                  Write-Host "Qualys Cloud Agent service found: $($qualysService.Status)"
                  
                  # Check installation path
                  $qualysPath = "C:\Program Files\Qualys\QualysAgent"
                  if (Test-Path $qualysPath) {
                      Write-Host "Qualys Cloud Agent is already installed at: $qualysPath"
                      
                      # Get version information if available
                      $agentExe = Join-Path $qualysPath "QualysAgent.exe"
                      if (Test-Path $agentExe) {
                          $version = (Get-Item $agentExe).VersionInfo.FileVersion
                          Write-Host "Installed version: $version"
                      }
                      
                      Write-Host "Skipping installation - Qualys Cloud Agent already present"
                      exit 0
                  }
              } else {
                  Write-Host "Qualys Cloud Agent is not installed. Proceeding with installation..."
              }

      - name: VerifyInstallerExists
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Verifying Qualys Cloud Agent installer availability..."

              # Expected path after S3 sync from windows-applications-s3 component
              $installerPath = "C:\Temp\ServerInstalls\Applications\qualys\QualysCloudAgent_x64.msi"

              if (!(Test-Path $installerPath)) {
                  Write-Error "Qualys Cloud Agent installer not found at: $installerPath"
                  Write-Host "Please ensure the windows-applications-s3 component has run successfully"
                  Write-Host "and that QualysCloudAgent_x64.msi exists in S3 at: s3://sgt-imagebuilder/windows/applications/qualys/"
                  exit 1
              }

              # Verify file size (should be at least 1MB for a valid MSI)
              $fileSize = (Get-Item $installerPath).Length
              $fileSizeMB = [math]::Round($fileSize / 1MB, 2)

              if ($fileSize -lt 1MB) {
                  Write-Error "Installer file appears to be invalid (size: $fileSizeMB MB)"
                  exit 1
              }

              Write-Host "Installer found: $installerPath"
              Write-Host "Installer size: $fileSizeMB MB"
              Write-Host "Installer verification successful"

      - name: InstallQualysAgent
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Installing Qualys Cloud Agent..."

              $installerPath = "C:\Temp\ServerInstalls\Applications\qualys\QualysCloudAgent_x64.msi"
              $logPath = "C:\Temp\ServerInstalls\Logs\QualysAgent-Install.log"

              # Create logs directory if it doesn't exist
              $logDir = Split-Path $logPath
              if (!(Test-Path $logDir)) {
                  New-Item -ItemType Directory -Path $logDir -Force | Out-Null
                  Write-Host "Created log directory: $logDir"
              }

              # Get parameters
              $customerId = "{{ CustomerId }}"
              $activationId = "{{ ActivationId }}"
              $webServiceUri = "{{ WebServiceUri }}"
              $proxyServer = "{{ ProxyServer }}"
              $proxyPort = "{{ ProxyPort }}"

              Write-Host "Installation Configuration:"
              Write-Host "  Customer ID: $customerId"
              Write-Host "  Activation ID: $activationId"
              Write-Host "  Web Service URI: $webServiceUri"

              # Build MSI installation arguments
              $msiArgs = @(
                  "/i"
                  "`"$installerPath`""
                  "CustomerId={$customerId}"
                  "ActivationId={$activationId}"
                  "WebServiceUri=$webServiceUri"
                  "/qn"  # Quiet mode, no user interaction
                  "/norestart"  # Do not restart automatically
                  "/L*v"  # Verbose logging
                  "`"$logPath`""
              )

              # Add proxy settings if provided
              if ($proxyServer -and $proxyPort) {
                  Write-Host "  Proxy Server: ${proxyServer}:${proxyPort}"
                  $msiArgs += "ProxyServer=$proxyServer"
                  $msiArgs += "ProxyPort=$proxyPort"
              } elseif ($proxyServer) {
                  Write-Host "  Proxy Server: $proxyServer (no port specified)"
                  $msiArgs += "ProxyServer=$proxyServer"
              }

              Write-Host "`nStarting Qualys Cloud Agent installation..."
              Write-Host "This may take several minutes..."
              Write-Host "Installation log: $logPath"

              # Execute MSI installation
              $process = Start-Process -FilePath "msiexec.exe" -ArgumentList $msiArgs -Wait -PassThru -NoNewWindow

              Write-Host "`nInstallation process completed with exit code: $($process.ExitCode)"

              # MSI Exit Codes:
              # 0 = Success
              # 1641 = Success, reboot initiated
              # 3010 = Success, reboot required
              # Other = Error

              switch ($process.ExitCode) {
                  0 { 
                      Write-Host "SUCCESS: Qualys Cloud Agent installed successfully"
                  }
                  1641 { 
                      Write-Host "SUCCESS: Qualys Cloud Agent installed successfully (reboot initiated)"
                  }
                  3010 { 
                      Write-Host "SUCCESS: Qualys Cloud Agent installed successfully (reboot required)"
                  }
                  1603 {
                      Write-Error "FAILED: Fatal error during installation"
                      Write-Host "Check installation log for details: $logPath"
                      exit 1
                  }
                  1618 {
                      Write-Error "FAILED: Another installation is already in progress"
                      exit 1
                  }
                  1619 {
                      Write-Error "FAILED: Installation package could not be opened"
                      exit 1
                  }
                  default { 
                      Write-Error "FAILED: Installation failed with exit code: $($process.ExitCode)"
                      Write-Host "Check installation log for details: $logPath"
                      
                      # Try to display last 20 lines of log if available
                      if (Test-Path $logPath) {
                          Write-Host "`nLast 20 lines of installation log:"
                          Get-Content $logPath -Tail 20 | ForEach-Object { Write-Host "  $_" }
                      }
                      exit 1
                  }
              }

      - name: VerifyServiceStatus
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Verifying Qualys Cloud Agent service status..."

              # Wait a few seconds for service to register
              Start-Sleep -Seconds 5

              $qualysService = Get-Service -Name "QualysAgent" -ErrorAction SilentlyContinue

              if (!$qualysService) {
                  Write-Error "Qualys Cloud Agent service not found after installation"
                  exit 1
              }

              Write-Host "Service Name: $($qualysService.Name)"
              Write-Host "Service Display Name: $($qualysService.DisplayName)"
              Write-Host "Service Status: $($qualysService.Status)"
              Write-Host "Service Start Type: $($qualysService.StartType)"

              # Ensure service is set to automatic start
              if ($qualysService.StartType -ne "Automatic") {
                  Write-Host "Setting service to start automatically..."
                  Set-Service -Name "QualysAgent" -StartupType Automatic
                  Write-Host "Service startup type updated to Automatic"
              }

              # Start the service if it's not running
              if ($qualysService.Status -ne "Running") {
                  Write-Host "Starting Qualys Cloud Agent service..."
                  Start-Service -Name "QualysAgent"
                  Start-Sleep -Seconds 3
                  
                  $qualysService = Get-Service -Name "QualysAgent"
                  if ($qualysService.Status -eq "Running") {
                      Write-Host "SUCCESS: Qualys Cloud Agent service is now running"
                  } else {
                      Write-Warning "Service started but status is: $($qualysService.Status)"
                  }
              } else {
                  Write-Host "SUCCESS: Qualys Cloud Agent service is already running"
              }

      - name: VerifyInstallationFiles
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Verifying Qualys Cloud Agent installation files..."

              $installPath = "C:\Program Files\Qualys\QualysAgent"

              if (!(Test-Path $installPath)) {
                  Write-Error "Qualys Cloud Agent installation directory not found: $installPath"
                  exit 1
              }

              Write-Host "Installation directory found: $installPath"

              # Check for key files
              $requiredFiles = @(
                  "QualysAgent.exe",
                  "QualysProxy.exe"
              )

              $missingFiles = @()
              foreach ($file in $requiredFiles) {
                  $filePath = Join-Path $installPath $file
                  if (Test-Path $filePath) {
                      $version = (Get-Item $filePath).VersionInfo.FileVersion
                      Write-Host "  Found: $file (Version: $version)"
                  } else {
                      Write-Warning "  Missing: $file"
                      $missingFiles += $file
                  }
              }

              if ($missingFiles.Count -gt 0) {
                  Write-Warning "Some expected files are missing, but installation may still be valid"
              } else {
                  Write-Host "All expected files are present"
              }

              Write-Host "Installation files verification completed"

  - name: validate
    steps:
      - name: ValidateQualysAgent
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Final validation of Qualys Cloud Agent installation..."

              # Check service exists and is running
              $qualysService = Get-Service -Name "QualysAgent" -ErrorAction SilentlyContinue

              if (!$qualysService) {
                  Write-Error "VALIDATION FAILED: Qualys Cloud Agent service not found"
                  exit 1
              }

              if ($qualysService.Status -ne "Running") {
                  Write-Error "VALIDATION FAILED: Qualys Cloud Agent service is not running (Status: $($qualysService.Status))"
                  exit 1
              }

              # Check installation directory
              $installPath = "C:\Program Files\Qualys\QualysAgent"
              if (!(Test-Path $installPath)) {
                  Write-Error "VALIDATION FAILED: Installation directory not found"
                  exit 1
              }

              # Check for main executable
              $agentExe = Join-Path $installPath "QualysAgent.exe"
              if (!(Test-Path $agentExe)) {
                  Write-Error "VALIDATION FAILED: QualysAgent.exe not found"
                  exit 1
              }

              Write-Host "========================================="
              Write-Host "VALIDATION SUCCESS"
              Write-Host "========================================="
              Write-Host "Qualys Cloud Agent is properly installed and running"
              Write-Host "Service Status: $($qualysService.Status)"
              Write-Host "Service Start Type: $($qualysService.StartType)"
              Write-Host "Installation Path: $installPath"

              $version = (Get-Item $agentExe).VersionInfo.FileVersion
              if ($version) {
                  Write-Host "Agent Version: $version"
              }

              Write-Host "========================================="
              Write-Host ""
              Write-Host "The agent will automatically register with the Qualys platform"
              Write-Host "and begin scanning according to your subscription configuration."
              Write-Host ""
