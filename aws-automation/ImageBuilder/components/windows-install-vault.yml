# AWS ImageBuilder Component: Install HashiCorp Vault from S3
# Author: <PERSON><PERSON>
# Date: 2025-10-07
# Description: Install HashiCorp Vault from pre-downloaded S3 applications

name: windows-install-vault
description: Install HashiCorp Vault executable from S3 applications directory
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: InstallVaultFromS3
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              <#
                  .SYNOPSIS
                  Installs HashiCorp Vault from S3 applications directory
                  
                  .DESCRIPTION
                  This component installs HashiCorp Vault from the pre-downloaded
                  applications in C:\Temp\ServerInstalls\Applications\vault\
                  This provides a reliable fallback when GitHub direct downloads fail.
                  
                  .NOTES
                  Author: R<PERSON> van Z<PERSON>
                  Version: 1.0.0
                  Date: 2025-10-07
                  
                  Prerequisites:
                  - windows-applications-s3 component must run first
                  - S3 bucket must contain vault/vault.exe
              #>
              
              # Configuration
              $sourceDir = "C:\Temp\ServerInstalls\Applications\vault"
              $installDir = "C:\Program Files\HashiCorp\Vault"
              $executableName = "vault.exe"
              $versionFile = "vault-version.txt"
              
              # Function to write structured log messages
              function Write-Log {
                  param(
                      [string]$Message,
                      [ValidateSet("INFO", "WARN", "ERROR", "SUCCESS")]
                      [string]$Level = "INFO"
                  )
                  
                  $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
                  $logMessage = "[$timestamp] [$Level] $Message"
                  
                  switch ($Level) {
                      "INFO" { Write-Host $logMessage -ForegroundColor Cyan }
                      "WARN" { Write-Host $logMessage -ForegroundColor Yellow }
                      "ERROR" { Write-Host $logMessage -ForegroundColor Red }
                      "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
                  }
              }
              
              # Function to create directory if it doesn't exist
              function New-Directory {
                  param([string]$Path)
                  
                  if (!(Test-Path $Path)) {
                      New-Item -ItemType Directory -Path $Path -Force | Out-Null
                      Write-Log "Created directory: $Path" "SUCCESS"
                  } else {
                      Write-Log "Directory already exists: $Path" "INFO"
                  }
              }
              
              # Function to get version information
              function Get-VaultVersionInfo {
                  param([string]$VersionFilePath)
                  
                  try {
                      if (Test-Path $VersionFilePath) {
                          $content = Get-Content $VersionFilePath -Raw
                          $versionLine = $content -split "`n" | Where-Object { $_ -like "Version:*" } | Select-Object -First 1
                          if ($versionLine) {
                              $version = $versionLine -replace "Version:\s*", ""
                              return $version.Trim()
                          }
                      }
                      return "Unknown"
                  } catch {
                      Write-Log "Failed to read version info: $($_.Exception.Message)" "WARN"
                      return "Unknown"
                  }
              }
              
              try {
                  Write-Log "Starting HashiCorp Vault installation from S3 applications" "INFO"
                  Write-Log "Source directory: $sourceDir" "INFO"
                  Write-Log "Install directory: $installDir" "INFO"
                  
                  # Check if source directory exists
                  if (!(Test-Path $sourceDir)) {
                      throw "Source directory not found: $sourceDir. Ensure windows-applications-s3 component runs first."
                  }
                  
                  # Check if vault.exe exists in source
                  $sourceExePath = Join-Path $sourceDir $executableName
                  if (!(Test-Path $sourceExePath)) {
                      throw "Vault executable not found: $sourceExePath. Check S3 sync and bucket contents."
                  }
                  
                  Write-Log "Found Vault executable: $sourceExePath" "SUCCESS"
                  
                  # Get file information
                  $sourceFile = Get-Item $sourceExePath
                  $fileSize = [math]::Round($sourceFile.Length / 1MB, 2)
                  $fileDate = $sourceFile.LastWriteTime
                  Write-Log "File size: $fileSize MB" "INFO"
                  Write-Log "File date: $fileDate" "INFO"
                  
                  # Get version information if available
                  $versionFilePath = Join-Path $sourceDir $versionFile
                  $vaultVersion = Get-VaultVersionInfo -VersionFilePath $versionFilePath
                  Write-Log "Vault version: $vaultVersion" "INFO"
                  
                  # Create install directory
                  New-Directory -Path $installDir
                  
                  # Copy vault.exe to install directory
                  $targetExePath = Join-Path $installDir $executableName
                  Write-Log "Copying Vault executable to: $targetExePath" "INFO"
                  
                  try {
                      Copy-Item -Path $sourceExePath -Destination $targetExePath -Force
                      Write-Log "Vault executable copied successfully" "SUCCESS"
                  } catch {
                      throw "Failed to copy Vault executable: $($_.Exception.Message)"
                  }
                  
                  # Verify the installation
                  if (Test-Path $targetExePath) {
                      Write-Log "Verifying Vault installation..." "INFO"
                      
                      try {
                          # Test vault executable
                          $vaultOutput = & $targetExePath version 2>&1
                          
                          if ($LASTEXITCODE -eq 0) {
                              Write-Log "Vault installation verified successfully" "SUCCESS"
                              Write-Log "Vault version output: $vaultOutput" "INFO"
                          } else {
                              Write-Log "Vault executable test failed with exit code: $LASTEXITCODE" "WARN"
                              Write-Log "Output: $vaultOutput" "WARN"
                              Write-Log "Installation completed but verification failed" "WARN"
                          }
                      } catch {
                          Write-Log "Failed to test Vault executable: $($_.Exception.Message)" "WARN"
                          Write-Log "Installation completed but verification failed" "WARN"
                      }
                  } else {
                      throw "Vault executable not found at target location after copy"
                  }
                  
                  # Add to system PATH
                  Write-Log "Adding Vault to system PATH..." "INFO"
                  try {
                      $currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
                      
                      if ($currentPath -notlike "*$installDir*") {
                          $newPath = "$currentPath;$installDir"
                          [Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")
                          Write-Log "Added $installDir to system PATH" "SUCCESS"
                          Write-Log "PATH will be available after system restart or new sessions" "INFO"
                      } else {
                          Write-Log "Vault directory already in system PATH" "INFO"
                      }
                      
                  } catch {
                      Write-Log "Failed to update PATH: $($_.Exception.Message)" "WARN"
                      Write-Log "Vault can still be accessed via full path: $targetExePath" "INFO"
                  }
                  
                  # Copy version file if it exists
                  if (Test-Path $versionFilePath) {
                      try {
                          $targetVersionPath = Join-Path $installDir $versionFile
                          Copy-Item -Path $versionFilePath -Destination $targetVersionPath -Force
                          Write-Log "Version file copied to install directory" "INFO"
                      } catch {
                          Write-Log "Failed to copy version file: $($_.Exception.Message)" "WARN"
                      }
                  }
                  
                  # Set file permissions (optional security hardening)
                  try {
                      Write-Log "Setting file permissions..." "INFO"
                      
                      # Get current ACL
                      $acl = Get-Acl $targetExePath
                      
                      # Remove inheritance and copy existing permissions
                      $acl.SetAccessRuleProtection($true, $true)
                      
                      # Apply the modified ACL
                      Set-Acl -Path $targetExePath -AclObject $acl
                      
                      Write-Log "File permissions configured" "SUCCESS"
                  } catch {
                      Write-Log "Failed to set file permissions: $($_.Exception.Message)" "WARN"
                      Write-Log "This is not critical for functionality" "INFO"
                  }
                  
                  # Create installation summary
                  $installationSummary = @"
=== HashiCorp Vault Installation Summary ===
Installation Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss UTC')
Vault Version: $vaultVersion
Source Path: $sourceExePath
Install Path: $targetExePath
File Size: $fileSize MB
Source File Date: $fileDate
PATH Updated: Yes
Verification: $(if ($LASTEXITCODE -eq 0) { 'Passed' } else { 'Warning' })

Usage:
- Global access: vault <command>
- Direct access: "$targetExePath" <command>
- Version check: vault version

Notes:
- PATH changes take effect in new command sessions
- Vault is ready for use in your applications
- Version file available at: $(Join-Path $installDir $versionFile)
"@
                  
                  Write-Log "Installation completed successfully!" "SUCCESS"
                  Write-Host $installationSummary -ForegroundColor Green
                  
                  # Log final status
                  Write-Log "Vault executable location: $targetExePath" "INFO"
                  Write-Log "Vault version: $vaultVersion" "INFO"
                  Write-Log "Installation directory: $installDir" "INFO"
                  Write-Log "Global command available: vault" "INFO"
                  
              } catch {
                  Write-Log "Vault installation failed: $($_.Exception.Message)" "ERROR"
                  Write-Log "Troubleshooting steps:" "ERROR"
                  Write-Log "1. Verify windows-applications-s3 component ran successfully" "ERROR"
                  Write-Log "2. Check S3 bucket contains vault/vault.exe" "ERROR"
                  Write-Log "3. Verify S3 sync completed without errors" "ERROR"
                  Write-Log "4. Check source directory: $sourceDir" "ERROR"
                  
                  # Additional diagnostics
                  Write-Log "=== DIAGNOSTIC INFORMATION ===" "ERROR"
                  
                  if (Test-Path "C:\Temp\ServerInstalls\Applications") {
                      Write-Log "Applications directory contents:" "INFO"
                      try {
                          Get-ChildItem "C:\Temp\ServerInstalls\Applications" -Recurse -ErrorAction SilentlyContinue | 
                              ForEach-Object { Write-Log "  $($_.FullName)" "INFO" }
                      } catch {
                          Write-Log "Failed to list applications directory" "ERROR"
                      }
                  } else {
                      Write-Log "Applications directory does not exist" "ERROR"
                  }
                  
                  exit 1
              }
