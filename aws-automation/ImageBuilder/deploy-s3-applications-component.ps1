# Deploy S3 Applications Copy Component to AWS ImageBuilder
# Author: <PERSON><PERSON>
# Date: 2025-09-29
# Description: Deploy the copy-s3-applications component to AWS ImageBuilder

param(
    [Parameter(Mandatory=$false)]
    [string]$Region = "af-south-1",
    
    [Parameter(Mandatory=$false)]
    [string]$ComponentName = "windows-applications-s3-simple-robust",

    [Parameter(Mandatory=$false)]
    [string]$ComponentVersion = "1.0.0",

    [Parameter(Mandatory=$false)]
    [string]$ComponentPath = "components/windows-applications-s3-simple-robust.yml",
    
    [Parameter(Mandatory=$false)]
    [switch]$ForceUpdate = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$DryRun = $false
)

# Function to write log messages
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry
}

# Function to test AWS configuration
function Test-AWSConfiguration {
    Write-Log "Testing AWS configuration..."
    
    try {
        $identity = aws sts get-caller-identity --query "Account" --output text 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Log "AWS configuration valid. Account: $identity" "SUCCESS"
            return $true
        } else {
            Write-Log "AWS configuration test failed: $identity" "ERROR"
            return $false
        }
    } catch {
        Write-Log "Error testing AWS configuration: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Function to check if component exists
function Test-ComponentExists {
    param([string]$Name)
    
    try {
        $existingComponent = aws imagebuilder list-components `
            --filters "name=name,values=$Name" `
            --query "componentVersionList[0].arn" `
            --output text `
            --region $Region 2>$null
        
        if ($existingComponent -and $existingComponent -ne "None") {
            return $existingComponent
        }
        return $null
    } catch {
        return $null
    }
}

# Function to deploy the component
function Deploy-S3ApplicationsComponent {
    Write-Log "Deploying S3 Applications Copy Component..." "INFO"
    
    # Validate component file exists
    if (!(Test-Path $ComponentPath)) {
        Write-Log "Component file not found: $ComponentPath" "ERROR"
        return $false
    }
    
    # Check if component already exists
    $existingArn = Test-ComponentExists -Name $ComponentName
    if ($existingArn -and !$ForceUpdate) {
        Write-Log "Component $ComponentName already exists: $existingArn" "WARNING"
        Write-Log "Use -ForceUpdate to create a new version" "INFO"
        return $true
    }
    
    if ($DryRun) {
        Write-Log "DRY RUN: Would create component from $ComponentPath" "INFO"
        return $true
    }
    
    try {
        Write-Log "Creating ImageBuilder component: $ComponentName" "INFO"
        Write-Log "Component file: $ComponentPath" "INFO"
        Write-Log "Version: $ComponentVersion" "INFO"
        Write-Log "Region: $Region" "INFO"
        
        $result = aws imagebuilder create-component `
            --cli-input-yaml "file://$ComponentPath" `
            --region $Region 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            $componentArn = ($result | ConvertFrom-Json).componentBuildVersionArn
            Write-Log "Component created successfully!" "SUCCESS"
            Write-Log "Component ARN: $componentArn" "SUCCESS"
            return $true
        } else {
            if ($result -like "*already exists*") {
                Write-Log "Component already exists with this version" "WARNING"
                Write-Log "Consider incrementing the version in the YAML file" "INFO"
                return $true
            } else {
                Write-Log "Failed to create component: $result" "ERROR"
                return $false
            }
        }
    } catch {
        Write-Log "Error creating component: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Function to list existing components
function Show-ExistingComponents {
    Write-Log "Listing existing custom components..." "INFO"
    
    try {
        $components = aws imagebuilder list-components `
            --owner Self `
            --query "componentVersionList[*].[name,version,arn]" `
            --output table `
            --region $Region 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host $components
        } else {
            Write-Log "Could not list components: $components" "WARNING"
        }
    } catch {
        Write-Log "Error listing components: $($_.Exception.Message)" "WARNING"
    }
}

# Main execution
Write-Log "Starting S3 Applications Component deployment..." "INFO"
Write-Log "Component: $ComponentName" "INFO"
Write-Log "Version: $ComponentVersion" "INFO"
Write-Log "Region: $Region" "INFO"

if ($DryRun) {
    Write-Log "DRY RUN MODE - No changes will be made" "WARNING"
}

try {
    # Test AWS configuration
    if (!(Test-AWSConfiguration)) {
        Write-Log "AWS configuration test failed. Please check your credentials and region." "ERROR"
        exit 1
    }
    
    # Show existing components
    Show-ExistingComponents
    
    # Deploy the component
    if (Deploy-S3ApplicationsComponent) {
        Write-Log "Deployment completed successfully!" "SUCCESS"
        Write-Log "" "INFO"
        Write-Log "Next steps:" "INFO"
        Write-Log "1. Add this component to your ImageBuilder recipe" "INFO"
        Write-Log "2. Set environment variables S3_DEPLOYMENT_BUCKET and AWS_DEFAULT_REGION if needed" "INFO"
        Write-Log "3. Ensure your ImageBuilder instance profile has S3 read permissions" "INFO"
        Write-Log "" "INFO"
        Write-Log "Example recipe entry:" "INFO"
        Write-Log "- name: $ComponentName" "INFO"
        Write-Log "  parameters: []" "INFO"
        Write-Log "" "INFO"
        Write-Log "Note: Component uses default values:" "INFO"
        Write-Log "  S3 Bucket: sgt-imagebuilder" "INFO"
        Write-Log "  Region: $Region" "INFO"
    } else {
        Write-Log "Deployment failed!" "ERROR"
        exit 1
    }
} catch {
    Write-Log "Unexpected error: $($_.Exception.Message)" "ERROR"
    exit 1
}

Write-Log "Script completed." "INFO"
