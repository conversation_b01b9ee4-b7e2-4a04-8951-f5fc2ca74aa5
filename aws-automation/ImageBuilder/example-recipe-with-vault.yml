# AWS ImageBuilder Recipe: Windows Server with <PERSON><PERSON><PERSON><PERSON><PERSON> Vault
# Author: <PERSON><PERSON>
# Date: 2025-10-07
# Description: Complete Windows Server setup with <PERSON>hi<PERSON>orp Vault installation options

name: windows-server-2022-with-vault
description: Windows Server 2022 with <PERSON><PERSON><PERSON><PERSON><PERSON> Vault and standard applications
version: 1.0.0

# Parent image - Windows Server 2022 Base
parentImage: arn:aws:imagebuilder:af-south-1:aws:image/windows-server-2022-english-full-base-x86/x.x.x

# Instance configuration
instanceConfiguration:
  instanceType: m5.large
  systemsManagerAgent:
    uninstallAfterBuild: false

# Build components in execution order
components:
  # 1. Ensure AWS CLI is available (required for S3 operations)
  - name: ensure-aws-cli
    parameters: []

  # 2. Copy applications from S3 (includes Vault if synced)
  - name: windows-applications-s3
    parameters:
      - name: S3BucketName
        value: sgt-imagebuilder
      - name: S3Prefix
        value: windows/applications
      - name: TargetDirectory
        value: C:\Temp\ServerInstalls\Applications

  # 3. Install Vault - Two options available:

  # Option A: Install from S3 (Recommended - uses pre-synced version)
  - name: windows-install-vault
    parameters: []

  # Option B: Install directly from GitHub (Alternative - comment out Option A if using this)
  # - name: install-hashicorp-vault
  #   parameters: []

  # 4. Install other standard applications
  - name: install-dotnet48
    parameters: []

  - name: install-powershell7
    parameters: []

  # 5. Configure Windows settings
  - name: windows-registry-security
    parameters: []

  - name: windows-registry-performance
    parameters: []

  - name: windows-firewall-base
    parameters: []

  # 6. Install additional tools
  - name: windows-tools-bginfo
    parameters: []

# Test components (optional - for validation)
tests:
  - name: VaultInstallationTest
    action: ExecutePowerShell
    inputs:
      commands:
        - |
          # Test Vault installation
          Write-Host "Testing HashiCorp Vault installation..."
          
          $vaultPath = "C:\Program Files\HashiCorp\Vault\vault.exe"
          
          if (Test-Path $vaultPath) {
              Write-Host "✓ Vault executable found at: $vaultPath" -ForegroundColor Green
              
              try {
                  $version = & $vaultPath version 2>&1
                  if ($LASTEXITCODE -eq 0) {
                      Write-Host "✓ Vault version check successful: $version" -ForegroundColor Green
                  } else {
                      Write-Host "✗ Vault version check failed" -ForegroundColor Red
                      exit 1
                  }
              } catch {
                  Write-Host "✗ Failed to execute Vault: $($_.Exception.Message)" -ForegroundColor Red
                  exit 1
              }
              
              # Test PATH configuration
              try {
                  $pathTest = vault version 2>&1
                  if ($LASTEXITCODE -eq 0) {
                      Write-Host "✓ Vault accessible via PATH" -ForegroundColor Green
                  } else {
                      Write-Host "⚠ Vault not accessible via PATH (may require session restart)" -ForegroundColor Yellow
                  }
              } catch {
                  Write-Host "⚠ Vault not accessible via PATH (may require session restart)" -ForegroundColor Yellow
              }
              
          } else {
              Write-Host "✗ Vault executable not found at expected location" -ForegroundColor Red
              Write-Host "Expected: $vaultPath" -ForegroundColor Red
              exit 1
          }
          
          Write-Host "Vault installation test completed successfully!" -ForegroundColor Green

# Distribution settings
distributionConfiguration:
  - region: af-south-1
    amiDistributionConfiguration:
      name: windows-server-2022-vault-{{ imagebuilder:buildDate }}
      description: Windows Server 2022 with HashiCorp Vault and standard applications
      amiTags:
        Name: windows-server-2022-vault
        Environment: "{{ imagebuilder:buildDate }}"
        VaultIncluded: "true"
        BaseImage: windows-server-2022
        BuildDate: "{{ imagebuilder:buildDate }}"
        Component-Vault: "true"
        Component-DotNet48: "true"
        Component-PowerShell7: "true"

# Infrastructure configuration
infrastructureConfiguration:
  name: windows-vault-infrastructure
  description: Infrastructure for building Windows images with Vault
  instanceProfileName: EC2InstanceProfileForImageBuilder
  securityGroupIds:
    - sg-xxxxxxxxx  # Replace with your security group
  subnetId: subnet-xxxxxxxxx  # Replace with your subnet
  terminateInstanceOnFailure: true
  
  # Instance metadata options
  instanceMetadataOptions:
    httpTokens: required
    httpPutResponseHopLimit: 2

# Schedule (optional)
schedule:
  scheduleExpression: cron(0 2 * * SUN *)  # Weekly on Sunday at 2 AM UTC
  pipelineExecutionStartCondition: EXPRESSION_MATCH_AND_DEPENDENCY_UPDATES_AVAILABLE
