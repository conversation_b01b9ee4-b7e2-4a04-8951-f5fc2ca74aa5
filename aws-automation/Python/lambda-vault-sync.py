"""
AWS Lambda Function: <PERSON><PERSON><PERSON><PERSON><PERSON> Vault S3 Synchronization
Author: <PERSON><PERSON>
Date: 2025-10-07
Description: Automatically sync latest HashiCorp Vault releases to S3 bucket

This Lambda function can be triggered by:
- CloudWatch Events (scheduled)
- SNS notifications
- Manual invocation

Environment Variables Required:
- S3_BUCKET_NAME: Target S3 bucket name
- S3_PREFIX: S3 prefix/folder (default: windows/applications/vault)
- AWS_REGION: AWS region (default: af-south-1)
"""

import json
import boto3
import requests
import zipfile
import tempfile
import os
from datetime import datetime
from typing import Dict, Any, Optional
import logging

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Configuration from environment variables
S3_BUCKET_NAME = os.environ.get('S3_BUCKET_NAME')
S3_PREFIX = os.environ.get('S3_PREFIX', 'windows/applications/vault')
AWS_REGION = os.environ.get('AWS_REGION', 'af-south-1')
GITHUB_REPO = 'hashicorp/vault'
VERSION_FILE = 'vault-version.txt'

# Initialize AWS clients
s3_client = boto3.client('s3', region_name=AWS_REGION)


def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Main Lambda handler function
    """
    try:
        logger.info("Starting HashiCorp Vault S3 synchronization")
        logger.info(f"Target S3: s3://{S3_BUCKET_NAME}/{S3_PREFIX}")
        
        # Validate required environment variables
        if not S3_BUCKET_NAME:
            raise ValueError("S3_BUCKET_NAME environment variable is required")
        
        # Get force update flag from event
        force_update = event.get('force_update', False)
        
        # Get current S3 version
        current_s3_version = get_s3_vault_version()
        logger.info(f"Current S3 version: {current_s3_version or 'None'}")
        
        # Get latest GitHub release
        latest_release = get_latest_github_release()
        logger.info(f"Latest GitHub version: {latest_release['version']}")
        
        # Check if update is needed
        update_needed = force_update or (current_s3_version != latest_release['version'])
        
        if not update_needed:
            logger.info("S3 version is already up to date")
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'success': True,
                    'action': 'NoUpdateNeeded',
                    'current_version': current_s3_version,
                    'latest_version': latest_release['version'],
                    's3_location': f"s3://{S3_BUCKET_NAME}/{S3_PREFIX}/vault.exe",
                    'timestamp': datetime.utcnow().isoformat()
                })
            }
        
        logger.info("Update needed. Downloading and uploading new version...")
        
        # Download and upload Vault
        upload_result = download_and_upload_vault(latest_release)
        
        result = {
            'success': True,
            'action': 'Updated',
            'previous_version': current_s3_version,
            'new_version': latest_release['version'],
            's3_location': f"s3://{S3_BUCKET_NAME}/{upload_result['vault_key']}",
            'version_file': f"s3://{S3_BUCKET_NAME}/{upload_result['version_key']}",
            'published_at': latest_release['published_at'],
            'timestamp': datetime.utcnow().isoformat()
        }
        
        logger.info("Vault synchronization completed successfully")
        
        return {
            'statusCode': 200,
            'body': json.dumps(result)
        }
        
    except Exception as e:
        logger.error(f"Vault synchronization failed: {str(e)}")
        
        error_result = {
            'success': False,
            'error': str(e),
            's3_bucket': S3_BUCKET_NAME,
            's3_prefix': S3_PREFIX,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        return {
            'statusCode': 500,
            'body': json.dumps(error_result)
        }


def get_s3_vault_version() -> Optional[str]:
    """
    Get current Vault version from S3
    """
    try:
        version_key = f"{S3_PREFIX}/{VERSION_FILE}"
        
        response = s3_client.get_object(Bucket=S3_BUCKET_NAME, Key=version_key)
        content = response['Body'].read().decode('utf-8')
        
        # Extract version from content
        for line in content.split('\n'):
            if line.startswith('Version:'):
                return line.split(':', 1)[1].strip()
        
        return None
        
    except s3_client.exceptions.NoSuchKey:
        logger.info("No existing version found in S3")
        return None
    except Exception as e:
        logger.warning(f"Failed to get current S3 version: {str(e)}")
        return None


def get_latest_github_release() -> Dict[str, Any]:
    """
    Get latest GitHub release information
    """
    try:
        release_url = f"https://api.github.com/repos/{GITHUB_REPO}/releases/latest"
        
        response = requests.get(release_url, timeout=30)
        response.raise_for_status()
        
        release_info = response.json()
        
        # Find Windows x64 ZIP asset
        asset = None
        for a in release_info['assets']:
            if any(pattern in a['name'].lower() for pattern in ['windows_amd64.zip', 'win64.zip', 'windows-amd64.zip']):
                asset = a
                break
        
        if not asset:
            raise ValueError("Could not find Windows x64 asset in release")
        
        return {
            'version': release_info['tag_name'],
            'download_url': asset['browser_download_url'],
            'file_name': asset['name'],
            'size': asset['size'],
            'published_at': release_info['published_at']
        }
        
    except Exception as e:
        logger.error(f"Failed to get GitHub release info: {str(e)}")
        raise


def download_and_upload_vault(release_info: Dict[str, Any]) -> Dict[str, str]:
    """
    Download Vault from GitHub and upload to S3
    """
    try:
        logger.info(f"Downloading Vault {release_info['version']}...")
        
        # Download ZIP file to temporary location
        with tempfile.TemporaryDirectory() as temp_dir:
            zip_path = os.path.join(temp_dir, release_info['file_name'])
            extract_dir = os.path.join(temp_dir, 'extracted')
            
            # Download
            response = requests.get(release_info['download_url'], timeout=300)
            response.raise_for_status()
            
            with open(zip_path, 'wb') as f:
                f.write(response.content)
            
            logger.info(f"Download completed. Size: {len(response.content) / 1024 / 1024:.2f} MB")
            
            # Extract ZIP file
            logger.info("Extracting Vault executable...")
            os.makedirs(extract_dir, exist_ok=True)
            
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
            
            # Find vault.exe
            vault_exe_path = None
            for root, dirs, files in os.walk(extract_dir):
                if 'vault.exe' in files:
                    vault_exe_path = os.path.join(root, 'vault.exe')
                    break
            
            if not vault_exe_path:
                raise ValueError("vault.exe not found in extracted files")
            
            logger.info("Vault executable extracted successfully")
            
            # Upload to S3
            return upload_vault_to_s3(vault_exe_path, release_info)
            
    except Exception as e:
        logger.error(f"Failed to download/upload Vault: {str(e)}")
        raise


def upload_vault_to_s3(vault_exe_path: str, release_info: Dict[str, Any]) -> Dict[str, str]:
    """
    Upload Vault executable and version file to S3
    """
    try:
        logger.info("Uploading Vault to S3...")
        
        # Upload vault.exe
        vault_key = f"{S3_PREFIX}/vault.exe"
        
        with open(vault_exe_path, 'rb') as f:
            s3_client.put_object(
                Bucket=S3_BUCKET_NAME,
                Key=vault_key,
                Body=f,
                ContentType='application/octet-stream'
            )
        
        # Create and upload version file
        version_content = f"""Version: {release_info['version']}
Published: {release_info['published_at']}
FileName: {release_info['file_name']}
Size: {release_info['size']}
DownloadUrl: {release_info['download_url']}
SyncDate: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}
"""
        
        version_key = f"{S3_PREFIX}/{VERSION_FILE}"
        
        s3_client.put_object(
            Bucket=S3_BUCKET_NAME,
            Key=version_key,
            Body=version_content.encode('utf-8'),
            ContentType='text/plain'
        )
        
        logger.info(f"Vault {release_info['version']} uploaded successfully to S3")
        logger.info(f"S3 Location: s3://{S3_BUCKET_NAME}/{vault_key}")
        
        return {
            'vault_key': vault_key,
            'version_key': version_key,
            'version': release_info['version']
        }
        
    except Exception as e:
        logger.error(f"Failed to upload to S3: {str(e)}")
        raise


# For local testing
if __name__ == "__main__":
    # Set environment variables for testing
    os.environ['S3_BUCKET_NAME'] = 'your-test-bucket'
    os.environ['S3_PREFIX'] = 'windows/applications/vault'
    os.environ['AWS_REGION'] = 'af-south-1'
    
    # Test event
    test_event = {'force_update': False}
    
    result = lambda_handler(test_event, None)
    print(json.dumps(result, indent=2))
