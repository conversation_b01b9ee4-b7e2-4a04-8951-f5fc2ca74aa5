# Domain Join with <PERSON><PERSON><PERSON><PERSON><PERSON> Vault

This document describes how to use HashiCorp Vault for secure domain join operations instead of AWS Secrets Manager.

## Overview

The `SSM-Command-JoinDomainWithVault.yaml` document provides a secure way to join EC2 instances to Active Directory domains using HashiCorp Vault for credential management. This approach offers several advantages over traditional methods:

- **Centralized Secret Management**: All credentials stored in Vault
- **Dynamic Secrets**: Vault can generate time-limited credentials
- **Audit Trail**: Complete audit logging of secret access
- **Fine-grained Access Control**: Vault policies control who can access what
- **Multiple Auth Methods**: Support for AWS IAM, tokens, and other methods

## Prerequisites

### 1. HashiCorp Vault Installation
Ensure Vault is installed on your EC2 instances using one of these methods:

**Option A: Use the automated installation component**
```yaml
components:
  - name: windows-install-vault  # or install-hashicorp-vault
```

**Option B: Manual installation**
- Download Vault from HashiCorp releases
- Place `vault.exe` in `C:\Program Files\HashiCorp\Vault\`
- Add to system PATH

### 2. Vault Server Setup
You need a running Vault server with:
- Network connectivity from EC2 instances
- Appropriate authentication method configured
- KV secrets engine enabled
- Proper policies and roles configured

### 3. Domain Join Credentials in Vault
Store domain join credentials in Vault with this structure:

```json
{
  "domainJoinUserName": "DOMAIN\\serviceaccount",
  "domainJoinPassword": "SecurePassword123!",
  "domainName": "company.local"
}
```

## Vault Configuration

### 1. Enable KV Secrets Engine
```bash
vault secrets enable -path=secret kv-v2
```

### 2. Store Domain Join Credentials
```bash
vault kv put secret/domain-join/credentials \
    domainJoinUserName="DOMAIN\\serviceaccount" \
    domainJoinPassword="SecurePassword123!" \
    domainName="company.local"
```

### 3. Configure AWS Authentication (Recommended)
```bash
# Enable AWS auth method
vault auth enable aws

# Configure AWS auth
vault write auth/aws/config/client \
    secret_key="YOUR_SECRET_KEY" \
    access_key="YOUR_ACCESS_KEY" \
    region="af-south-1"

# Create role for domain join
vault write auth/aws/role/domain-join-role \
    auth_type=iam \
    bound_iam_principal_arn="arn:aws:iam::ACCOUNT:role/EC2-DomainJoin-Role" \
    policies="domain-join-policy" \
    max_ttl=1h
```

### 4. Create Vault Policy
```bash
vault policy write domain-join-policy - <<EOF
# Allow reading domain join credentials
path "secret/data/domain-join/credentials" {
  capabilities = ["read"]
}

# Allow token self-renewal
path "auth/token/renew-self" {
  capabilities = ["update"]
}
EOF
```

## IAM Configuration

### EC2 Instance Role
Create an IAM role for EC2 instances with these permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ssm:GetParameter",
                "ssm:GetParameters",
                "ssm:SendCommand",
                "ssm:UpdateInstanceInformation"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "sts:GetCallerIdentity"
            ],
            "Resource": "*"
        }
    ]
}
```

## Usage Examples

### 1. Basic Domain Join with AWS Authentication
```bash
aws ssm send-command \
    --document-name "SSM-Command-JoinDomainWithVault" \
    --instance-ids "i-1234567890abcdef0" \
    --parameters \
        Domain="company.local" \
        VaultAddress="https://vault.company.com:8200" \
        VaultSecretPath="secret/domain-join/credentials" \
        VaultAuthMethod="aws" \
        VaultRole="domain-join-role" \
        LocalAdminGroups="Domain Admins,IT Support"
```

### 2. Domain Join with Token Authentication
```bash
aws ssm send-command \
    --document-name "SSM-Command-JoinDomainWithVault" \
    --instance-ids "i-1234567890abcdef0" \
    --parameters \
        Domain="company.local" \
        VaultAddress="https://vault.company.com:8200" \
        VaultSecretPath="secret/domain-join/credentials" \
        VaultAuthMethod="token" \
        VaultToken="/vault/tokens/domain-join-token" \
        LocalAdminGroups="Domain Admins"
```

### 3. Domain Join with Custom Computer Name and OU
```bash
aws ssm send-command \
    --document-name "SSM-Command-JoinDomainWithVault" \
    --instance-ids "i-1234567890abcdef0" \
    --parameters \
        Domain="company.local" \
        VaultAddress="https://vault.company.com:8200" \
        VaultSecretPath="secret/domain-join/credentials" \
        VaultAuthMethod="aws" \
        VaultRole="domain-join-role" \
        ComputerName="WEB-SERVER-01" \
        TargetOU="OU=WebServers,OU=AWS,DC=company,DC=local" \
        LocalAdminGroups="Web Admins,Domain Admins"
```

## Parameters Reference

| Parameter | Required | Description | Example |
|-----------|----------|-------------|---------|
| `Domain` | Yes | FQDN of AD domain | `company.local` |
| `VaultAddress` | Yes | Vault server URL | `https://vault.company.com:8200` |
| `VaultSecretPath` | Yes | Path to credentials in Vault | `secret/domain-join/credentials` |
| `VaultAuthMethod` | Yes | Authentication method | `aws`, `token`, `userpass` |
| `VaultRole` | No | Vault role for AWS auth | `domain-join-role` |
| `VaultToken` | No | Token for token auth | `/vault/tokens/token` |
| `LocalAdminGroups` | No | Comma-separated AD groups | `Domain Admins,IT Support` |
| `ComputerName` | No | New computer name | `WEB-SERVER-01` |
| `TargetOU` | No | Target OU DN | `OU=Servers,DC=company,DC=local` |
| `RestartAfterJoin` | No | Restart after join | `true` (default) |
| `VaultSkipTLSVerify` | No | Skip TLS verification | `false` (default) |

## Security Best Practices

### 1. Use AWS Authentication
- Preferred method for EC2 instances
- No long-lived credentials to manage
- Automatic credential rotation
- Tied to IAM roles and policies

### 2. Implement Least Privilege
- Create specific Vault policies for domain join
- Limit secret access to required paths only
- Use time-limited tokens when possible

### 3. Enable Audit Logging
```bash
vault audit enable file file_path=/vault/logs/audit.log
```

### 4. Use TLS Encryption
- Always use HTTPS for Vault communication
- Implement proper certificate validation
- Avoid `VaultSkipTLSVerify=true` in production

### 5. Rotate Credentials Regularly
- Use Vault's dynamic secrets when possible
- Implement regular password rotation
- Monitor credential usage through audit logs

## Troubleshooting

### Common Issues

1. **Vault CLI Not Found**
   ```
   Error: HashiCorp Vault CLI not found
   Solution: Ensure Vault is installed and in PATH
   ```

2. **Authentication Failed**
   ```
   Error: AWS auth failed
   Solution: Check IAM role and Vault role configuration
   ```

3. **Secret Not Found**
   ```
   Error: Failed to read secret from Vault
   Solution: Verify secret path and Vault policies
   ```

4. **Network Connectivity**
   ```
   Error: Connection timeout to Vault server
   Solution: Check security groups and network ACLs
   ```

### Debug Commands

```powershell
# Test Vault connectivity
vault status

# Test authentication
vault auth -method=aws role=domain-join-role

# Test secret access
vault kv get secret/domain-join/credentials

# Check token information
vault token lookup
```

### Log Locations

- **SSM Command Output**: AWS Systems Manager Console
- **Vault Audit Logs**: Vault server audit log location
- **Windows Event Logs**: System and Application logs
- **Domain Join Logs**: Windows Security logs

## Migration from Secrets Manager

To migrate from the existing Secrets Manager solution:

1. **Export existing secrets** from AWS Secrets Manager
2. **Import secrets** into Vault using the same structure
3. **Update SSM documents** to use the new Vault-based command
4. **Test thoroughly** in development environment
5. **Update automation** to use new document name

### Migration Script Example

```powershell
# Export from Secrets Manager
$secret = Get-SECSecretValue -SecretId "arn:aws:secretsmanager:region:account:secret:domain-join"
$secretData = $secret.SecretString | ConvertFrom-Json

# Import to Vault
vault kv put secret/domain-join/credentials \
    domainJoinUserName="$($secretData.domainJoinUserName)" \
    domainJoinPassword="$($secretData.domainJoinPassword)" \
    domainName="$($secretData.domainName)"
```

## Comparison: Vault vs Secrets Manager

| Feature | HashiCorp Vault | AWS Secrets Manager |
|---------|-----------------|-------------------|
| **Cost** | Self-hosted (free) or Enterprise | Pay per secret + API calls |
| **Dynamic Secrets** | ✅ Full support | ❌ Limited |
| **Audit Logging** | ✅ Comprehensive | ✅ CloudTrail integration |
| **Multi-Cloud** | ✅ Cloud agnostic | ❌ AWS only |
| **Secret Engines** | ✅ Multiple types | ❌ Key-value only |
| **Authentication** | ✅ Multiple methods | ✅ IAM integration |
| **Encryption** | ✅ Multiple options | ✅ KMS integration |
| **High Availability** | ✅ Clustering support | ✅ Multi-AZ |

## Conclusion

Using HashiCorp Vault for domain join operations provides enhanced security, flexibility, and audit capabilities compared to traditional methods. The integrated SSM command document makes it easy to deploy while maintaining security best practices.

For production deployments, ensure proper Vault server setup, implement appropriate security policies, and thoroughly test the domain join process in a development environment first.
