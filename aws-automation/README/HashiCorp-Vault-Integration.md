# HashiCorp Vault Integration for Windows AMI

This document describes the complete solution for integrating HashiCorp Vault into your Windows AMI build process with automated version management.

## Overview

The solution provides:
- **Automated Download**: Latest Vault releases from GitHub
- **S3 Synchronization**: Automated uploads to your S3 bucket
- **AMI Integration**: ImageBuilder component for installation
- **Version Management**: Tracking and notifications
- **Monitoring**: CloudWatch alarms and SNS notifications

## Architecture

```
GitHub Releases → Lambda Function → S3 Bucket → ImageBuilder → Windows AMI
                      ↓
                CloudWatch Events (Scheduled)
                      ↓
                SNS Notifications
```

## Components

### 1. ImageBuilder Component
**File**: `ImageBuilder/components/install-hashicorp-vault.yml`

Installs Vault during AMI build:
- Downloads latest release from GitHub
- Installs to `C:\Program Files\HashiCorp\Vault\`
- Adds to system PATH
- Includes fallback mechanism

### 2. S3 Synchronization Script
**File**: `S3/windows/scripts/Sync-HashiCorpVault.ps1`

PowerShell script for manual synchronization:
- Downloads latest Vault release
- Uploads to S3 with version tracking
- Supports force updates
- JSO<PERSON> output for automation

### 3. Lambda Function
**File**: `Python/lambda-vault-sync.py`

Automated synchronization service:
- Scheduled execution via CloudWatch Events
- Downloads from GitHub API
- Uploads to S3 with metadata
- SNS notifications for status

### 4. Infrastructure Template
**File**: `Terraform/vault-sync-infrastructure.yaml`

CloudFormation template deploying:
- Lambda function with IAM roles
- CloudWatch Events scheduling
- SNS topic for notifications
- CloudWatch alarms for monitoring

### 5. Deployment Script
**File**: `S3/windows/scripts/Deploy-VaultSyncInfrastructure.ps1`

Complete deployment automation:
- Deploys CloudFormation stack
- Updates Lambda function code
- Performs initial synchronization
- Validates deployment

## Installation Location Recommendation

**Recommended**: `C:\Program Files\HashiCorp\Vault\`

**Why not System32?**
- Security best practices
- Easier maintenance and updates
- Compliance with security frameworks
- Cleaner PATH management
- Follows HashiCorp conventions

## Quick Start

### 1. Deploy Infrastructure

```powershell
# Basic deployment
.\Deploy-VaultSyncInfrastructure.ps1 -StackName "vault-sync" -S3BucketName "sgt-imagebuilder"

# With notifications and initial sync
.\Deploy-VaultSyncInfrastructure.ps1 `
    -StackName "vault-sync" `
    -S3BucketName "sgt-imagebuilder" `
    -NotificationEmail "<EMAIL>" `
    -InitialSync
```

### 2. Add to ImageBuilder Recipe

```yaml
components:
  - name: install-hashicorp-vault
    parameters: []
```

### 3. Manual Synchronization

```powershell
# Check for updates and sync if needed
.\Sync-HashiCorpVault.ps1 -S3BucketName "sgt-imagebuilder"

# Force update regardless of version
.\Sync-HashiCorpVault.ps1 -S3BucketName "sgt-imagebuilder" -ForceUpdate
```

## Configuration Options

### Schedule Expressions

```yaml
# Daily at 9 AM UTC
ScheduleExpression: "cron(0 9 * * ? *)"

# Every 6 hours
ScheduleExpression: "rate(6 hours)"

# Weekly on Sundays
ScheduleExpression: "cron(0 9 ? * SUN *)"
```

### S3 Structure

```
s3://your-bucket/
└── windows/applications/vault/
    ├── vault.exe              # Latest Vault executable
    └── vault-version.txt      # Version metadata
```

### Version File Format

```
Version: v1.15.2
Published: 2024-01-15T10:30:00Z
FileName: vault_1.15.2_windows_amd64.zip
Size: 45678901
DownloadUrl: https://github.com/hashicorp/vault/releases/download/v1.15.2/vault_1.15.2_windows_amd64.zip
SyncDate: 2025-10-07 14:30:00 UTC
```

## Monitoring and Alerts

### CloudWatch Alarms

1. **Lambda Errors**: Triggers on function failures
2. **Lambda Duration**: Alerts on long execution times
3. **S3 Upload Failures**: Monitors upload success

### SNS Notifications

- Deployment status
- Version updates
- Error alerts
- Schedule execution status

## Manual Operations

### Force Update

```bash
# AWS CLI
aws lambda invoke \
    --function-name vault-sync-lambda \
    --payload '{"force_update": true}' \
    response.json

# PowerShell
Invoke-RestMethod -Uri "https://api.lambda.region.amazonaws.com/2015-03-31/functions/vault-sync/invocations" \
    -Method POST \
    -Body '{"force_update": true}'
```

### Check Current Version

```bash
# Download version file
aws s3 cp s3://sgt-imagebuilder/windows/applications/vault/vault-version.txt ./

# View content
cat vault-version.txt
```

### Update Schedule

```bash
# Update CloudWatch Events rule
aws events put-rule \
    --name vault-sync-schedule \
    --schedule-expression "rate(12 hours)"
```

## Troubleshooting

### Common Issues

1. **GitHub API Rate Limiting**
   - Solution: Implement exponential backoff
   - Monitor: CloudWatch logs for 403 errors

2. **S3 Upload Failures**
   - Check: IAM permissions
   - Verify: Bucket exists and is accessible

3. **Lambda Timeout**
   - Increase: Function timeout (current: 5 minutes)
   - Monitor: Duration CloudWatch alarm

4. **Version Detection Issues**
   - Check: GitHub release asset naming
   - Verify: Windows x64 ZIP availability

### Log Locations

- **Lambda Logs**: `/aws/lambda/vault-sync-lambda`
- **ImageBuilder Logs**: EC2 Image Builder console
- **S3 Access Logs**: Configure bucket logging

### Debug Commands

```powershell
# Test S3 access
aws s3 ls s3://sgt-imagebuilder/windows/applications/vault/

# Check Lambda function
aws lambda get-function --function-name vault-sync-lambda

# View recent executions
aws logs filter-log-events \
    --log-group-name /aws/lambda/vault-sync-lambda \
    --start-time $(date -d '1 hour ago' +%s)000
```

## Security Considerations

### IAM Permissions

Lambda function requires:
- `s3:GetObject`, `s3:PutObject` on vault prefix
- `s3:ListBucket` with prefix condition
- `sns:Publish` for notifications

### Network Security

- Lambda runs in AWS managed VPC
- HTTPS-only downloads from GitHub
- S3 encryption in transit and at rest

### Access Control

- Vault executable stored in protected S3 location
- Version file provides audit trail
- CloudWatch logs for monitoring access

## Cost Optimization

### Lambda Costs
- **Memory**: 256MB (sufficient for downloads)
- **Timeout**: 5 minutes (typical execution: 1-2 minutes)
- **Frequency**: Daily (adjust based on needs)

### S3 Costs
- **Storage**: ~50MB per version
- **Requests**: Minimal (1-2 operations per sync)
- **Transfer**: Free within AWS region

### Estimated Monthly Cost
- Lambda: $0.10-0.50
- S3: $0.01-0.05
- CloudWatch: $0.05-0.10
- **Total**: <$1.00/month

## Best Practices

1. **Version Pinning**: Consider pinning to specific versions for production
2. **Testing**: Test new versions in development before production
3. **Backup**: Keep previous versions in S3 for rollback
4. **Monitoring**: Set up comprehensive alerting
5. **Documentation**: Update fallback versions periodically

## Integration with Existing Workflow

### ImageBuilder Recipe Example

```yaml
name: windows-server-with-vault
description: Windows Server 2022 with HashiCorp Vault
version: 1.0.0
components:
  - name: ensure-aws-cli
  - name: install-hashicorp-vault
  - name: windows-applications-s3
    parameters:
      - name: S3BucketName
        value: sgt-imagebuilder
      - name: S3Prefix
        value: windows/applications
```

### Terraform Integration

```hcl
resource "aws_imagebuilder_component" "vault" {
  name     = "install-hashicorp-vault"
  platform = "Windows"
  version  = "1.0.0"
  uri      = "s3://sgt-imagebuilder/components/install-hashicorp-vault.yml"
}

resource "aws_imagebuilder_image_recipe" "windows_vault" {
  name         = "windows-server-vault"
  parent_image = "arn:aws:imagebuilder:af-south-1:aws:image/windows-server-2022-english-full-base-x86/x.x.x"
  version      = "1.0.0"

  component {
    component_arn = aws_imagebuilder_component.vault.arn
  }
}
```

## Support and Maintenance

### Regular Tasks

1. **Monthly**: Review CloudWatch logs and metrics
2. **Quarterly**: Update fallback version in ImageBuilder component
3. **Annually**: Review and update IAM permissions

### Version Updates

The system automatically handles version updates, but manual intervention may be needed for:
- Major version changes with breaking changes
- Asset naming pattern changes on GitHub
- HashiCorp release process modifications

### Contact Information

For issues or questions:
- Check CloudWatch logs first
- Review GitHub releases for any changes
- Consult HashiCorp documentation for Vault-specific issues
