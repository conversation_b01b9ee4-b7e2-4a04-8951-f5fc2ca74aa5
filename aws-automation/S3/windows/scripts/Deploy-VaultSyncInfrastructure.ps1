<#
    .SYNOPSIS
    Deploys HashiCorp Vault synchronization infrastructure
    
    .DESCRIPTION
    This script deploys the complete infrastructure for automatically
    synchronizing HashiCorp Vault releases to your S3 bucket, including:
    - Lambda function for downloading and uploading
    - CloudWatch Events for scheduling
    - SNS notifications for alerts
    - CloudWatch alarms for monitoring
    
    .PARAMETER StackName
    CloudFormation stack name
    
    .PARAMETER S3BucketName
    S3 bucket name for storing applications
    
    .PARAMETER S3Prefix
    S3 prefix for Vault files
    
    .PARAMETER ScheduleExpression
    CloudWatch Events schedule expression (e.g., "rate(1 day)", "cron(0 9 * * ? *)")
    
    .PARAMETER NotificationEmail
    Email address for notifications (optional)
    
    .PARAMETER Region
    AWS region
    
    .PARAMETER InitialSync
    Perform initial sync after deployment
    
    .PARAMETER OutputJson
    Output results in JSON format
    
    .EXAMPLE
    .\Deploy-VaultSyncInfrastructure.ps1 -StackName "vault-sync" -S3BucketName "sgt-imagebuilder"
    
    .EXAMPLE
    .\Deploy-VaultSyncInfrastructure.ps1 -StackName "vault-sync" -S3Bucket<PERSON>ame "sgt-imagebuilder" -NotificationEmail "<EMAIL>" -InitialSync
    
    .NOTES
    Author: Rudi van Zyl
    Version: 1.0.0
    Date: 2025-10-07
    
    #requires -Version 5.1
#>

param(
    [Parameter(Mandatory=$true)]
    [string]$StackName,
    
    [Parameter(Mandatory=$true)]
    [string]$S3BucketName,
    
    [Parameter(Mandatory=$false)]
    [string]$S3Prefix = "windows/applications/vault",
    
    [Parameter(Mandatory=$false)]
    [string]$ScheduleExpression = "rate(1 day)",
    
    [Parameter(Mandatory=$false)]
    [string]$NotificationEmail = "",
    
    [Parameter(Mandatory=$false)]
    [string]$Region = "af-south-1",
    
    [Parameter(Mandatory=$false)]
    [switch]$InitialSync = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$OutputJson = $false
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to write structured log messages
function Write-Log {
    param(
        [string]$Message,
        [ValidateSet("INFO", "WARN", "ERROR", "SUCCESS")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "INFO" { Write-Host $logMessage -ForegroundColor Cyan }
        "WARN" { Write-Host $logMessage -ForegroundColor Yellow }
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
    }
}

# Function to check if AWS CLI is available
function Test-AwsCli {
    try {
        $version = aws --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Log "AWS CLI found: $version" "SUCCESS"
            return $true
        } else {
            Write-Log "AWS CLI not found or not working" "ERROR"
            return $false
        }
    } catch {
        Write-Log "AWS CLI not available: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Function to deploy CloudFormation stack
function Deploy-CloudFormationStack {
    param(
        [string]$StackName,
        [string]$TemplateFile,
        [hashtable]$Parameters,
        [string]$Region
    )
    
    try {
        Write-Log "Deploying CloudFormation stack: $StackName" "INFO"
        
        # Build parameter string
        $parameterString = ""
        foreach ($key in $Parameters.Keys) {
            if ($Parameters[$key] -ne "") {
                $parameterString += "ParameterKey=$key,ParameterValue=$($Parameters[$key]) "
            }
        }
        $parameterString = $parameterString.Trim()
        
        # Check if stack exists
        $stackExists = $false
        try {
            aws cloudformation describe-stacks --stack-name $StackName --region $Region --output json | Out-Null
            if ($LASTEXITCODE -eq 0) {
                $stackExists = $true
                Write-Log "Stack exists, updating..." "INFO"
            }
        } catch {
            Write-Log "Stack does not exist, creating..." "INFO"
        }
        
        # Deploy or update stack
        if ($stackExists) {
            $command = "aws cloudformation update-stack --stack-name $StackName --template-body file://$TemplateFile --parameters $parameterString --capabilities CAPABILITY_NAMED_IAM --region $Region"
        } else {
            $command = "aws cloudformation create-stack --stack-name $StackName --template-body file://$TemplateFile --parameters $parameterString --capabilities CAPABILITY_NAMED_IAM --region $Region"
        }
        
        Write-Log "Executing: $command" "INFO"
        Invoke-Expression $command
        
        if ($LASTEXITCODE -ne 0) {
            throw "CloudFormation deployment failed"
        }
        
        # Wait for stack completion
        Write-Log "Waiting for stack deployment to complete..." "INFO"
        $waitCommand = "aws cloudformation wait stack-$(if ($stackExists) { 'update' } else { 'create' })-complete --stack-name $StackName --region $Region"
        Invoke-Expression $waitCommand
        
        if ($LASTEXITCODE -ne 0) {
            throw "Stack deployment did not complete successfully"
        }
        
        Write-Log "CloudFormation stack deployed successfully" "SUCCESS"
        
        # Get stack outputs
        $outputsJson = aws cloudformation describe-stacks --stack-name $StackName --region $Region --query "Stacks[0].Outputs" --output json
        if ($LASTEXITCODE -eq 0) {
            $outputs = $outputsJson | ConvertFrom-Json
            return $outputs
        } else {
            Write-Log "Failed to get stack outputs" "WARN"
            return @()
        }
        
    } catch {
        Write-Log "CloudFormation deployment failed: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to update Lambda function code
function Update-LambdaFunctionCode {
    param(
        [string]$FunctionName,
        [string]$CodeFile,
        [string]$Region
    )
    
    try {
        Write-Log "Updating Lambda function code..." "INFO"
        
        # Create temporary zip file
        $tempDir = [System.IO.Path]::GetTempPath()
        $zipFile = Join-Path $tempDir "lambda-vault-sync.zip"
        
        # Remove existing zip if it exists
        if (Test-Path $zipFile) {
            Remove-Item $zipFile -Force
        }
        
        # Create zip file with Python code
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        $zip = [System.IO.Compression.ZipFile]::Open($zipFile, 'Create')
        
        # Add the Python file as index.py
        $entry = $zip.CreateEntry("index.py")
        $entryStream = $entry.Open()
        $fileContent = Get-Content $CodeFile -Raw -Encoding UTF8
        $bytes = [System.Text.Encoding]::UTF8.GetBytes($fileContent)
        $entryStream.Write($bytes, 0, $bytes.Length)
        $entryStream.Close()
        $zip.Dispose()
        
        # Update Lambda function
        aws lambda update-function-code --function-name $FunctionName --zip-file "fileb://$zipFile" --region $Region | Out-Null
        
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Lambda function code updated successfully" "SUCCESS"
        } else {
            throw "Failed to update Lambda function code"
        }
        
        # Cleanup
        Remove-Item $zipFile -Force -ErrorAction SilentlyContinue
        
    } catch {
        Write-Log "Failed to update Lambda function code: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to perform initial sync
function Invoke-InitialSync {
    param(
        [string]$FunctionName,
        [string]$Region
    )
    
    try {
        Write-Log "Performing initial Vault sync..." "INFO"
        
        $payload = '{"force_update": true, "source": "initial_deployment"}'
        $responseFile = Join-Path $env:TEMP "vault-sync-response.json"
        
        aws lambda invoke --function-name $FunctionName --payload $payload $responseFile --region $Region | Out-Null
        
        if ($LASTEXITCODE -eq 0 -and (Test-Path $responseFile)) {
            $response = Get-Content $responseFile -Raw | ConvertFrom-Json
            Remove-Item $responseFile -Force -ErrorAction SilentlyContinue
            
            if ($response.statusCode -eq 200) {
                $body = $response.body | ConvertFrom-Json
                Write-Log "Initial sync completed successfully" "SUCCESS"
                Write-Log "Action: $($body.action)" "INFO"
                Write-Log "Version: $($body.new_version ?? $body.current_version)" "INFO"
                return $body
            } else {
                throw "Initial sync failed with status code: $($response.statusCode)"
            }
        } else {
            throw "Failed to invoke Lambda function for initial sync"
        }
        
    } catch {
        Write-Log "Initial sync failed: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Main execution
try {
    Write-Log "Starting Vault sync infrastructure deployment" "INFO"
    Write-Log "Stack Name: $StackName" "INFO"
    Write-Log "S3 Bucket: $S3BucketName" "INFO"
    Write-Log "S3 Prefix: $S3Prefix" "INFO"
    Write-Log "Schedule: $ScheduleExpression" "INFO"
    Write-Log "Region: $Region" "INFO"
    
    # Validate prerequisites
    if (-not (Test-AwsCli)) {
        throw "AWS CLI is required but not available"
    }
    
    # Find template file
    $scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
    $templateFile = Join-Path (Split-Path -Parent (Split-Path -Parent $scriptDir)) "Terraform\vault-sync-infrastructure.yaml"
    
    if (-not (Test-Path $templateFile)) {
        throw "CloudFormation template not found: $templateFile"
    }
    
    # Find Lambda code file
    $lambdaCodeFile = Join-Path (Split-Path -Parent (Split-Path -Parent $scriptDir)) "Python\lambda-vault-sync.py"
    
    if (-not (Test-Path $lambdaCodeFile)) {
        throw "Lambda code file not found: $lambdaCodeFile"
    }
    
    # Prepare CloudFormation parameters
    $cfParameters = @{
        S3BucketName = $S3BucketName
        S3Prefix = $S3Prefix
        ScheduleExpression = $ScheduleExpression
        NotificationEmail = $NotificationEmail
    }
    
    # Deploy CloudFormation stack
    $stackOutputs = Deploy-CloudFormationStack -StackName $StackName -TemplateFile $templateFile -Parameters $cfParameters -Region $Region
    
    # Extract function name from outputs
    $functionName = ($stackOutputs | Where-Object { $_.OutputKey -eq "LambdaFunctionName" }).OutputValue
    
    if ($functionName) {
        # Update Lambda function code
        Update-LambdaFunctionCode -FunctionName $functionName -CodeFile $lambdaCodeFile -Region $Region
        
        # Perform initial sync if requested
        $syncResult = $null
        if ($InitialSync) {
            $syncResult = Invoke-InitialSync -FunctionName $functionName -Region $Region
        }
        
        # Build final result
        $result = @{
            Success = $true
            StackName = $StackName
            FunctionName = $functionName
            S3Location = "s3://$S3BucketName/$S3Prefix/vault.exe"
            ScheduleExpression = $ScheduleExpression
            InitialSyncPerformed = $InitialSync
            SyncResult = $syncResult
            StackOutputs = $stackOutputs
            Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
        }
        
        Write-Log "=== DEPLOYMENT COMPLETED SUCCESSFULLY ===" "SUCCESS"
        Write-Log "Function Name: $functionName" "INFO"
        Write-Log "S3 Location: s3://$S3BucketName/$S3Prefix/vault.exe" "INFO"
        Write-Log "Schedule: $ScheduleExpression" "INFO"
        
        if ($InitialSync -and $syncResult) {
            Write-Log "Initial Sync: $($syncResult.action)" "INFO"
            Write-Log "Vault Version: $($syncResult.new_version ?? $syncResult.current_version)" "INFO"
        }
        
    } else {
        throw "Could not determine Lambda function name from stack outputs"
    }
    
    # Output results
    if ($OutputJson) {
        Write-Output ($result | ConvertTo-Json -Depth 10)
    }
    
    return $result
    
} catch {
    Write-Log "Deployment failed: $($_.Exception.Message)" "ERROR"
    
    $errorResult = @{
        Success = $false
        Error = $_.Exception.Message
        StackName = $StackName
        S3BucketName = $S3BucketName
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
    }
    
    if ($OutputJson) {
        Write-Output ($errorResult | ConvertTo-Json -Depth 5)
    }
    
    exit 1
}
