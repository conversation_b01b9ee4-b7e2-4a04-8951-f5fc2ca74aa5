<#
    .SYNOPSIS
    Synchronizes the latest HashiCorp Vault executable to S3 bucket
    
    .DESCRIPTION
    This script downloads the latest HashiCorp Vault release from GitHub
    and uploads it to your S3 bucket, maintaining version tracking and
    ensuring your AMI builds always have access to the latest version.
    
    .PARAMETER S3BucketName
    The name of the S3 bucket to upload to
    
    .PARAMETER S3Prefix
    The S3 prefix/folder path (default: "windows/applications/vault")
    
    .PARAMETER Region
    AWS region (default: "af-south-1")
    
    .PARAMETER ForceUpdate
    Force download and upload even if the version hasn't changed
    
    .PARAMETER OutputJson
    Output results in JSON format
    
    .EXAMPLE
    .\Sync-HashiCorpVault.ps1 -S3BucketName "sgt-imagebuilder" -S3Prefix "windows/applications/vault"
    
    .NOTES
    Author: R<PERSON>
    Version: 1.0.0
    Date: 2025-10-07
    
    #requires -Version 5.1
#>

param(
    [Parameter(Mandatory=$true)]
    [string]$S3BucketName,
    
    [Parameter(Mandatory=$false)]
    [string]$S3Prefix = "windows/applications/vault",
    
    [Parameter(Mandatory=$false)]
    [string]$Region = "af-south-1",
    
    [Parameter(Mandatory=$false)]
    [switch]$ForceUpdate = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$OutputJson = $false
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Configuration
$githubRepo = "hashicorp/vault"
$tempDir = "C:\Temp\VaultSync"
$versionFile = "vault-version.txt"

# Function to write structured log messages
function Write-Log {
    param(
        [string]$Message,
        [ValidateSet("INFO", "WARN", "ERROR", "SUCCESS")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "INFO" { Write-Host $logMessage -ForegroundColor Cyan }
        "WARN" { Write-Host $logMessage -ForegroundColor Yellow }
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
    }
}

# Function to get current version from S3
function Get-S3VaultVersion {
    param(
        [string]$BucketName,
        [string]$Prefix,
        [string]$Region
    )
    
    try {
        $versionKey = "$Prefix/$versionFile"
        $tempVersionFile = Join-Path $env:TEMP "current-vault-version.txt"
        
        # Try to download current version file
        aws s3 cp "s3://$BucketName/$versionKey" $tempVersionFile --region $Region 2>$null
        
        if ($LASTEXITCODE -eq 0 -and (Test-Path $tempVersionFile)) {
            $currentVersion = Get-Content $tempVersionFile -Raw
            Remove-Item $tempVersionFile -Force -ErrorAction SilentlyContinue
            return $currentVersion.Trim()
        } else {
            Write-Log "No existing version found in S3" "INFO"
            return $null
        }
        
    } catch {
        Write-Log "Failed to get current S3 version: $($_.Exception.Message)" "WARN"
        return $null
    }
}

# Function to get latest GitHub release
function Get-LatestGitHubRelease {
    param([string]$Repository)
    
    try {
        Write-Log "Fetching latest release information from GitHub..." "INFO"
        $releaseUrl = "https://api.github.com/repos/$Repository/releases/latest"
        
        # Use TLS 1.2
        [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
        
        $releaseInfo = Invoke-RestMethod -Uri $releaseUrl -UseBasicParsing -TimeoutSec 30
        
        # Find Windows x64 ZIP asset
        $asset = $releaseInfo.assets | Where-Object { 
            $_.name -like "*windows_amd64.zip" -or 
            $_.name -like "*win64.zip" -or
            $_.name -like "*windows-amd64.zip"
        } | Select-Object -First 1
        
        if (-not $asset) {
            throw "Could not find Windows x64 asset in release"
        }
        
        return @{
            Version = $releaseInfo.tag_name
            DownloadUrl = $asset.browser_download_url
            FileName = $asset.name
            Size = $asset.size
            PublishedAt = $releaseInfo.published_at
        }
        
    } catch {
        Write-Log "Failed to get GitHub release info: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to download and extract Vault
function Get-VaultExecutable {
    param(
        [object]$ReleaseInfo,
        [string]$DownloadDir
    )
    
    try {
        $zipPath = Join-Path $DownloadDir $ReleaseInfo.FileName
        $extractDir = Join-Path $DownloadDir "extracted"
        
        # Create directories
        New-Item -ItemType Directory -Path $DownloadDir -Force | Out-Null
        New-Item -ItemType Directory -Path $extractDir -Force | Out-Null
        
        # Download ZIP file
        Write-Log "Downloading Vault $($ReleaseInfo.Version)..." "INFO"
        Write-Log "URL: $($ReleaseInfo.DownloadUrl)" "INFO"
        
        Invoke-WebRequest -Uri $ReleaseInfo.DownloadUrl -OutFile $zipPath -UseBasicParsing -TimeoutSec 300
        
        if (Test-Path $zipPath) {
            $fileSize = (Get-Item $zipPath).Length
            Write-Log "Download completed. Size: $([math]::Round($fileSize / 1MB, 2)) MB" "SUCCESS"
        } else {
            throw "Downloaded file not found"
        }
        
        # Extract ZIP file
        Write-Log "Extracting Vault executable..." "INFO"
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        [System.IO.Compression.ZipFile]::ExtractToDirectory($zipPath, $extractDir)
        
        # Find vault.exe
        $vaultExe = Get-ChildItem -Path $extractDir -Name "vault.exe" -Recurse | Select-Object -First 1
        
        if ($vaultExe) {
            $vaultPath = Join-Path $extractDir $vaultExe
            Write-Log "Vault executable extracted successfully" "SUCCESS"
            return $vaultPath
        } else {
            throw "vault.exe not found in extracted files"
        }
        
    } catch {
        Write-Log "Failed to download/extract Vault: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to upload to S3
function Sync-VaultToS3 {
    param(
        [string]$VaultExePath,
        [object]$ReleaseInfo,
        [string]$BucketName,
        [string]$Prefix,
        [string]$Region
    )
    
    try {
        Write-Log "Uploading Vault to S3..." "INFO"
        
        # Upload vault.exe
        $vaultKey = "$Prefix/vault.exe"
        aws s3 cp $VaultExePath "s3://$BucketName/$vaultKey" --region $Region
        
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to upload vault.exe to S3"
        }
        
        # Create and upload version file
        $versionContent = @"
Version: $($ReleaseInfo.Version)
Published: $($ReleaseInfo.PublishedAt)
FileName: $($ReleaseInfo.FileName)
Size: $($ReleaseInfo.Size)
DownloadUrl: $($ReleaseInfo.DownloadUrl)
SyncDate: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC")
"@
        
        $tempVersionFile = Join-Path $env:TEMP $versionFile
        $versionContent | Out-File -FilePath $tempVersionFile -Encoding UTF8
        
        $versionKey = "$Prefix/$versionFile"
        aws s3 cp $tempVersionFile "s3://$BucketName/$versionKey" --region $Region
        
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to upload version file to S3"
        }
        
        # Cleanup temp version file
        Remove-Item $tempVersionFile -Force -ErrorAction SilentlyContinue
        
        Write-Log "Vault $($ReleaseInfo.Version) uploaded successfully to S3" "SUCCESS"
        Write-Log "S3 Location: s3://$BucketName/$vaultKey" "INFO"
        
        return @{
            VaultKey = $vaultKey
            VersionKey = $versionKey
            Version = $ReleaseInfo.Version
        }
        
    } catch {
        Write-Log "Failed to upload to S3: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Main execution
try {
    Write-Log "Starting HashiCorp Vault S3 synchronization" "INFO"
    Write-Log "Target S3: s3://$S3BucketName/$S3Prefix" "INFO"
    
    # Get current S3 version
    $currentS3Version = Get-S3VaultVersion -BucketName $S3BucketName -Prefix $S3Prefix -Region $Region
    Write-Log "Current S3 version: $(if ($currentS3Version) { $currentS3Version } else { 'None' })" "INFO"
    
    # Get latest GitHub release
    $latestRelease = Get-LatestGitHubRelease -Repository $githubRepo
    Write-Log "Latest GitHub version: $($latestRelease.Version)" "INFO"
    
    # Check if update is needed
    $updateNeeded = $ForceUpdate -or ($currentS3Version -ne $latestRelease.Version)
    
    if (-not $updateNeeded) {
        Write-Log "S3 version is already up to date" "SUCCESS"
        $result = @{
            Success = $true
            Action = "NoUpdateNeeded"
            CurrentVersion = $currentS3Version
            LatestVersion = $latestRelease.Version
            S3Location = "s3://$S3BucketName/$S3Prefix/vault.exe"
            Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
        }
    } else {
        Write-Log "Update needed. Downloading and uploading new version..." "INFO"
        
        # Create temp directory
        if (Test-Path $tempDir) {
            Remove-Item $tempDir -Recurse -Force
        }
        
        # Download and extract Vault
        $vaultExePath = Get-VaultExecutable -ReleaseInfo $latestRelease -DownloadDir $tempDir
        
        # Upload to S3
        $uploadResult = Sync-VaultToS3 -VaultExePath $vaultExePath -ReleaseInfo $latestRelease -BucketName $S3BucketName -Prefix $S3Prefix -Region $Region
        
        # Cleanup
        Write-Log "Cleaning up temporary files..." "INFO"
        Remove-Item $tempDir -Recurse -Force -ErrorAction SilentlyContinue
        
        $result = @{
            Success = $true
            Action = "Updated"
            PreviousVersion = $currentS3Version
            NewVersion = $latestRelease.Version
            S3Location = "s3://$S3BucketName/$($uploadResult.VaultKey)"
            VersionFile = "s3://$S3BucketName/$($uploadResult.VersionKey)"
            PublishedAt = $latestRelease.PublishedAt
            Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
        }
        
        Write-Log "Vault synchronization completed successfully" "SUCCESS"
    }
    
    # Output results
    if ($OutputJson) {
        Write-Output ($result | ConvertTo-Json -Depth 5)
    } else {
        Write-Log "=== SYNCHRONIZATION SUMMARY ===" "SUCCESS"
        Write-Log "Action: $($result.Action)" "INFO"
        Write-Log "Version: $($result.NewVersion ?? $result.CurrentVersion)" "INFO"
        Write-Log "S3 Location: $($result.S3Location)" "INFO"
    }
    
    return $result
    
} catch {
    Write-Log "Vault synchronization failed: $($_.Exception.Message)" "ERROR"
    
    $errorResult = @{
        Success = $false
        Error = $_.Exception.Message
        S3Bucket = $S3BucketName
        S3Prefix = $S3Prefix
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
    }
    
    if ($OutputJson) {
        Write-Output ($errorResult | ConvertTo-Json -Depth 3)
    }
    
    exit 1
}
