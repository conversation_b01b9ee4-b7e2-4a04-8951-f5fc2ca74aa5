<#
    .NOTES
    File Name: Test-DeploymentConfiguration.ps1
    Author: <PERSON><PERSON>
    Version: 1.0.0 - 03/09/2025
    Description: Configuration validation script for AWS EC2 Deployment Automation
    Note: This script validates configuration files and S3 bucket structure

    .NOTES
    Version 1.0.0     - Base Script
#>

param(
    [Parameter(Mandatory=$true)]
    [string]$S3BucketName,
    
    [Parameter(Mandatory=$false)]
    [string]$S3ConfigPrefix = "configs",
    
    [Parameter(Mandatory=$false)]
    [string]$Region = "af-south-1",
    
    [Parameter(Mandatory=$false)]
    [switch]$ValidateStructure = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$ValidateAllConfigs = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$OutputReport = $false
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to write structured log messages
function Write-LogMessage {
    param(
        [string]$Message,
        [ValidateSet("INFO", "WARN", "ERROR", "SUCCESS")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "INFO" { Write-Host $logMessage -ForegroundColor Cyan }
        "WARN" { Write-Host $logMessage -ForegroundColor Yellow }
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
    }
}

# Function to test S3 bucket access
function Test-S3BucketAccess {
    param(
        [string]$BucketName,
        [string]$Region
    )
    
    try {
        Write-LogMessage "Testing S3 bucket access: $BucketName" "INFO"
        
        $result = aws s3 ls "s3://$BucketName" --region $Region 2>&1
        
        if ($LASTEXITCODE -ne 0) {
            throw "Cannot access S3 bucket: $result"
        }
        
        Write-LogMessage "S3 bucket access successful" "SUCCESS"
        return $true
        
    } catch {
        Write-LogMessage "S3 bucket access failed: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Function to test configuration file existence
function Test-ConfigurationFiles {
    param(
        [string]$BucketName,
        [string]$ConfigPrefix,
        [string]$Region
    )
    
    $validationResults = @()
    
    try {
        Write-LogMessage "Validating configuration file structure" "INFO"
        
        # Test base configuration
        $baseConfigKey = "$ConfigPrefix/base_config.json"
        Write-LogMessage "Checking base configuration: $baseConfigKey" "INFO"
        
        $baseConfigExists = aws s3 ls "s3://$BucketName/$baseConfigKey" --region $Region 2>&1
        if ($LASTEXITCODE -eq 0) {
            $validationResults += @{
                File = $baseConfigKey
                Status = "EXISTS"
                Message = "Base configuration file found"
            }
            Write-LogMessage "✓ Base configuration exists" "SUCCESS"
        } else {
            $validationResults += @{
                File = $baseConfigKey
                Status = "MISSING"
                Message = "Base configuration file not found"
            }
            Write-LogMessage "✗ Base configuration missing" "ERROR"
        }
        
        # If base config exists, download and parse it to find specific configs
        if ($LASTEXITCODE -eq 0) {
            try {
                $tempFile = [System.IO.Path]::GetTempFileName()
                aws s3 cp "s3://$BucketName/$baseConfigKey" $tempFile --region $Region | Out-Null
                $baseConfig = Get-Content $tempFile -Raw | ConvertFrom-Json
                Remove-Item $tempFile -Force -ErrorAction SilentlyContinue
                
                # Check for each asset owner's specific configurations
                foreach ($assetOwner in $baseConfig.ASSET_OWNER) {
                    $assetConfig = $baseConfig.$assetOwner
                    
                    foreach ($appType in $assetConfig.APP_TYPE) {
                        $specificConfigKey = "$ConfigPrefix/${assetOwner}_${appType}_Config.json"
                        Write-LogMessage "Checking specific configuration: $specificConfigKey" "INFO"
                        
                        $specificConfigExists = aws s3 ls "s3://$BucketName/$specificConfigKey" --region $Region 2>&1
                        if ($LASTEXITCODE -eq 0) {
                            $validationResults += @{
                                File = $specificConfigKey
                                Status = "EXISTS"
                                Message = "Specific configuration file found"
                            }
                            Write-LogMessage "✓ $specificConfigKey exists" "SUCCESS"
                        } else {
                            $validationResults += @{
                                File = $specificConfigKey
                                Status = "MISSING"
                                Message = "Specific configuration file not found"
                            }
                            Write-LogMessage "✗ $specificConfigKey missing" "WARN"
                        }
                    }
                }
                
            } catch {
                Write-LogMessage "Failed to parse base configuration: $($_.Exception.Message)" "ERROR"
            }
        }
        
        return $validationResults
        
    } catch {
        Write-LogMessage "Configuration file validation failed: $($_.Exception.Message)" "ERROR"
        return $validationResults
    }
}

# Function to validate configuration content
function Test-ConfigurationContent {
    param(
        [string]$BucketName,
        [string]$ConfigPrefix,
        [string]$Region
    )
    
    $contentValidation = @()
    
    try {
        Write-LogMessage "Validating configuration content" "INFO"
        
        # Download and validate base configuration
        $baseConfigKey = "$ConfigPrefix/base_config.json"
        $tempFile = [System.IO.Path]::GetTempFileName()
        
        aws s3 cp "s3://$BucketName/$baseConfigKey" $tempFile --region $Region | Out-Null
        if ($LASTEXITCODE -ne 0) {
            throw "Cannot download base configuration for content validation"
        }
        
        $baseConfig = Get-Content $tempFile -Raw | ConvertFrom-Json
        Remove-Item $tempFile -Force -ErrorAction SilentlyContinue
        
        # Validate base configuration structure
        $requiredBaseFields = @("ASSET_OWNER")
        foreach ($field in $requiredBaseFields) {
            if (-not $baseConfig.$field) {
                $contentValidation += @{
                    File = $baseConfigKey
                    Field = $field
                    Status = "MISSING"
                    Message = "Required field '$field' missing from base configuration"
                }
                Write-LogMessage "✗ Base config missing field: $field" "ERROR"
            } else {
                $contentValidation += @{
                    File = $baseConfigKey
                    Field = $field
                    Status = "VALID"
                    Message = "Required field '$field' present"
                }
            }
        }
        
        # Validate each asset owner configuration
        foreach ($assetOwner in $baseConfig.ASSET_OWNER) {
            $assetConfig = $baseConfig.$assetOwner
            $requiredAssetFields = @("APP_TYPE", "ENV", "OS_TYPE", "OS_VERSION", "CLIENT")
            
            foreach ($field in $requiredAssetFields) {
                if (-not $assetConfig.$field) {
                    $contentValidation += @{
                        File = $baseConfigKey
                        Field = "$assetOwner.$field"
                        Status = "MISSING"
                        Message = "Required field '$field' missing from asset owner '$assetOwner'"
                    }
                    Write-LogMessage "✗ Asset '$assetOwner' missing field: $field" "ERROR"
                } else {
                    $contentValidation += @{
                        File = $baseConfigKey
                        Field = "$assetOwner.$field"
                        Status = "VALID"
                        Message = "Required field '$field' present for asset owner '$assetOwner'"
                    }
                }
            }
        }
        
        return $contentValidation
        
    } catch {
        Write-LogMessage "Configuration content validation failed: $($_.Exception.Message)" "ERROR"
        return $contentValidation
    }
}

# Main execution logic
try {
    Write-LogMessage "Starting Configuration Validation" "INFO"
    Write-LogMessage "S3 Bucket: $S3BucketName, Config Prefix: $S3ConfigPrefix, Region: $Region" "INFO"

    $validationReport = @{
        BucketName = $S3BucketName
        ConfigPrefix = $S3ConfigPrefix
        Region = $Region
        ValidationTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
        BucketAccess = $false
        FileValidation = @()
        ContentValidation = @()
        OverallStatus = "UNKNOWN"
        Summary = @{
            TotalFiles = 0
            ExistingFiles = 0
            MissingFiles = 0
            ValidContent = 0
            InvalidContent = 0
        }
    }

    # Step 1: Test S3 bucket access
    $validationReport.BucketAccess = Test-S3BucketAccess -BucketName $S3BucketName -Region $Region

    if (-not $validationReport.BucketAccess) {
        $validationReport.OverallStatus = "FAILED"
        Write-LogMessage "Cannot proceed with validation - S3 bucket access failed" "ERROR"
    } else {
        # Step 2: Test configuration file structure
        if ($ValidateStructure -or $ValidateAllConfigs) {
            $validationReport.FileValidation = Test-ConfigurationFiles -BucketName $S3BucketName -ConfigPrefix $S3ConfigPrefix -Region $Region

            # Calculate file summary
            $validationReport.Summary.TotalFiles = $validationReport.FileValidation.Count
            $validationReport.Summary.ExistingFiles = ($validationReport.FileValidation | Where-Object { $_.Status -eq "EXISTS" }).Count
            $validationReport.Summary.MissingFiles = ($validationReport.FileValidation | Where-Object { $_.Status -eq "MISSING" }).Count
        }

        # Step 3: Test configuration content
        if ($ValidateAllConfigs) {
            $validationReport.ContentValidation = Test-ConfigurationContent -BucketName $S3BucketName -ConfigPrefix $S3ConfigPrefix -Region $Region

            # Calculate content summary
            $validationReport.Summary.ValidContent = ($validationReport.ContentValidation | Where-Object { $_.Status -eq "VALID" }).Count
            $validationReport.Summary.InvalidContent = ($validationReport.ContentValidation | Where-Object { $_.Status -eq "MISSING" }).Count
        }

        # Determine overall status
        $hasErrors = $false
        if ($validationReport.Summary.MissingFiles -gt 0) { $hasErrors = $true }
        if ($validationReport.Summary.InvalidContent -gt 0) { $hasErrors = $true }

        $validationReport.OverallStatus = if ($hasErrors) { "FAILED" } else { "PASSED" }
    }

    # Output results
    if ($OutputReport) {
        Write-Output ($validationReport | ConvertTo-Json -Depth 10)
    } else {
        Write-LogMessage "=== VALIDATION SUMMARY ===" "INFO"
        Write-LogMessage "Overall Status: $($validationReport.OverallStatus)" $(if ($validationReport.OverallStatus -eq "PASSED") { "SUCCESS" } else { "ERROR" })
        Write-LogMessage "S3 Bucket Access: $(if ($validationReport.BucketAccess) { 'SUCCESS' } else { 'FAILED' })" $(if ($validationReport.BucketAccess) { "SUCCESS" } else { "ERROR" })

        if ($validationReport.FileValidation.Count -gt 0) {
            Write-LogMessage "File Validation: $($validationReport.Summary.ExistingFiles)/$($validationReport.Summary.TotalFiles) files exist" "INFO"
            if ($validationReport.Summary.MissingFiles -gt 0) {
                Write-LogMessage "Missing Files: $($validationReport.Summary.MissingFiles)" "WARN"
            }
        }

        if ($validationReport.ContentValidation.Count -gt 0) {
            Write-LogMessage "Content Validation: $($validationReport.Summary.ValidContent) valid, $($validationReport.Summary.InvalidContent) invalid" "INFO"
        }
    }

    # Return validation report
    return $validationReport

} catch {
    Write-LogMessage "Configuration validation failed: $($_.Exception.Message)" "ERROR"

    $errorReport = @{
        Error = $true
        Message = $_.Exception.Message
        ScriptName = $MyInvocation.MyCommand.Name
        LineNumber = $_.InvocationInfo.ScriptLineNumber
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
    }

    if ($OutputReport) {
        Write-Output ($errorReport | ConvertTo-Json -Depth 5)
    }

    exit 1
}
