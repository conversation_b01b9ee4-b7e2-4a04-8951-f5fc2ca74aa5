# SSM Parameter Store for Domain Join Configuration

This guide explains how to use SSM Parameter Store to manage domain join configuration for the `JoinDomainWithLocalAdmins` SSM Command document.

## Benefits of Using SSM Parameter Store

✅ **Centralized Configuration** - Store domain join settings in one place  
✅ **Environment-Specific** - Different settings for DEV, PPE, PRD  
✅ **Easy Updates** - Change configuration without modifying SSM documents  
✅ **Version Control** - Track parameter changes over time  
✅ **Secure** - Integrated with IAM for access control  

---

## Quick Start

### 1. Setup Parameters Using PowerShell Script

```powershell
# Run the setup script
.\Setup-DomainJoinParameters.ps1 `
  -Region "af-south-1" `
  -Domain "corp.example.com" `
  -DefaultLocalAdminGroups "CORP\IT-Admins,CORP\Server-Admins,CORP\AWS-Admins" `
  -TargetOU "OU=Servers,OU=AWS,DC=corp,DC=example,DC=com" `
  -Environment "ALL"

# To update existing parameters
.\Setup-DomainJoinParameters.ps1 `
  -Region "af-south-1" `
  -Domain "corp.example.com" `
  -DefaultLocalAdminGroups "CORP\IT-Admins,CORP\Server-Admins" `
  -Overwrite
```

### 2. Manual Parameter Creation (AWS CLI)

```bash
# Create domain parameter
aws ssm put-parameter \
  --name "/domain-join/domain" \
  --value "corp.example.com" \
  --type "String" \
  --description "Active Directory domain FQDN" \
  --region af-south-1

# Create local admin groups parameter
aws ssm put-parameter \
  --name "/domain-join/local-admin-groups" \
  --value "CORP\IT-Admins,CORP\Server-Admins,CORP\AWS-Admins" \
  --type "StringList" \
  --description "Default AD groups to add as local administrators" \
  --region af-south-1

# Create target OU parameter
aws ssm put-parameter \
  --name "/domain-join/target-ou" \
  --value "OU=Servers,OU=AWS,DC=corp,DC=example,DC=com" \
  --type "String" \
  --description "Default target OU for computer objects" \
  --region af-south-1
```

### 3. Create Environment-Specific Parameters

```bash
# DEV environment
aws ssm put-parameter \
  --name "/domain-join/dev/local-admin-groups" \
  --value "CORP\IT-Admins,CORP\Dev-Admins" \
  --type "StringList" \
  --region af-south-1

# PPE environment
aws ssm put-parameter \
  --name "/domain-join/ppe/local-admin-groups" \
  --value "CORP\IT-Admins,CORP\Server-Admins" \
  --type "StringList" \
  --region af-south-1

# PRD environment
aws ssm put-parameter \
  --name "/domain-join/prd/local-admin-groups" \
  --value "CORP\IT-Admins,CORP\Server-Admins,CORP\Security-Team" \
  --type "StringList" \
  --region af-south-1
```

---

## Parameter Structure

### Standard Parameters

| Parameter Name | Type | Description | Example Value |
|---------------|------|-------------|---------------|
| `/domain-join/domain` | String | AD domain FQDN | `corp.example.com` |
| `/domain-join/local-admin-groups` | StringList | Default local admin groups | `CORP\IT-Admins,CORP\Server-Admins` |
| `/domain-join/target-ou` | String | Default target OU | `OU=Servers,OU=AWS,DC=corp,DC=example,DC=com` |

### Environment-Specific Parameters

| Parameter Name | Type | Description |
|---------------|------|-------------|
| `/domain-join/dev/local-admin-groups` | StringList | DEV environment admin groups |
| `/domain-join/dev/target-ou` | String | DEV environment target OU |
| `/domain-join/ppe/local-admin-groups` | StringList | PPE environment admin groups |
| `/domain-join/ppe/target-ou` | String | PPE environment target OU |
| `/domain-join/prd/local-admin-groups` | StringList | PRD environment admin groups |
| `/domain-join/prd/target-ou` | String | PRD environment target OU |

---

## Using Parameters with SSM Command Document

### Option 1: Use Default Parameters (Recommended)

Leave the `Domain` and `LocalAdminGroups` parameters empty - the document will automatically retrieve values from Parameter Store:

```powershell
aws ssm send-command `
  --document-name "JoinDomainWithLocalAdmins" `
  --instance-ids "i-1234567890abcdef0" `
  --parameters `
    "SecretsManagerSecretArn=arn:aws:secretsmanager:af-south-1:123456789012:secret:domain-join-creds-abc123" `
    "Domain=" `
    "LocalAdminGroups=" `
    "Region=af-south-1" `
  --region af-south-1
```

### Option 2: Use Environment-Specific Parameters

Specify the parameter name for environment-specific configuration:

```powershell
aws ssm send-command `
  --document-name "JoinDomainWithLocalAdmins" `
  --instance-ids "i-1234567890abcdef0" `
  --parameters `
    "SecretsManagerSecretArn=arn:aws:secretsmanager:af-south-1:123456789012:secret:domain-join-creds-abc123" `
    "LocalAdminGroupsParameterName=/domain-join/prd/local-admin-groups" `
    "Region=af-south-1" `
  --region af-south-1
```

### Option 3: Override with Direct Values

Provide values directly to override Parameter Store:

```powershell
aws ssm send-command `
  --document-name "JoinDomainWithLocalAdmins" `
  --instance-ids "i-1234567890abcdef0" `
  --parameters `
    "Domain=corp.example.com" `
    "SecretsManagerSecretArn=arn:aws:secretsmanager:af-south-1:123456789012:secret:domain-join-creds-abc123" `
    "LocalAdminGroups=CORP\IT-Admins,CORP\Special-Admins" `
    "Region=af-south-1" `
  --region af-south-1
```

---

## Managing Parameters

### View All Domain Join Parameters

```powershell
# PowerShell
Get-SSMParametersByPath -Path "/domain-join" -Recursive $true -Region af-south-1

# AWS CLI
aws ssm get-parameters-by-path \
  --path "/domain-join" \
  --recursive \
  --region af-south-1
```

### Update a Parameter

```powershell
# PowerShell
Write-SSMParameter -Name "/domain-join/local-admin-groups" `
  -Value "CORP\IT-Admins,CORP\Server-Admins,CORP\New-Group" `
  -Type "StringList" `
  -Overwrite $true `
  -Region af-south-1

# AWS CLI
aws ssm put-parameter \
  --name "/domain-join/local-admin-groups" \
  --value "CORP\IT-Admins,CORP\Server-Admins,CORP\New-Group" \
  --type "StringList" \
  --overwrite \
  --region af-south-1
```

### Delete a Parameter

```powershell
# PowerShell
Remove-SSMParameter -Name "/domain-join/dev/local-admin-groups" -Region af-south-1

# AWS CLI
aws ssm delete-parameter \
  --name "/domain-join/dev/local-admin-groups" \
  --region af-south-1
```

### View Parameter History

```powershell
# PowerShell
Get-SSMParameterHistory -Name "/domain-join/local-admin-groups" -Region af-south-1

# AWS CLI
aws ssm get-parameter-history \
  --name "/domain-join/local-admin-groups" \
  --region af-south-1
```

---

## Required IAM Permissions

The EC2 instance needs the following IAM permissions to read from Parameter Store:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ssm:GetParameter",
        "ssm:GetParameters",
        "ssm:GetParametersByPath"
      ],
      "Resource": [
        "arn:aws:ssm:af-south-1:*:parameter/domain-join/*"
      ]
    }
  ]
}
```

---

## Best Practices

1. **Use Environment-Specific Parameters** - Different admin groups for DEV/PPE/PRD
2. **Version Your Parameters** - Use parameter versions to track changes
3. **Least Privilege** - Grant only necessary SSM permissions to EC2 instances
4. **Naming Convention** - Use consistent naming: `/domain-join/{environment}/{setting}`
5. **Documentation** - Add descriptions to all parameters
6. **Regular Audits** - Review parameter values periodically
7. **Backup** - Export parameters before making bulk changes

---

## Troubleshooting

### Parameter Not Found Error

```
Failed to retrieve parameter from SSM Parameter Store: ParameterNotFound
```

**Solution**: Create the parameter or provide the value directly in the SSM command

### Access Denied Error

```
Failed to retrieve parameter from SSM Parameter Store: AccessDeniedException
```

**Solution**: Add `ssm:GetParameter` permission to the EC2 instance IAM role

### Wrong Parameter Type

```
Parameter value is not a valid StringList
```

**Solution**: Ensure the parameter type matches (String vs StringList)

---

## Example Workflow

1. **Initial Setup** - Run `Setup-DomainJoinParameters.ps1` to create all parameters
2. **Deploy EC2 Instance** - Launch instance with appropriate IAM role
3. **Run Domain Join** - Execute SSM command without specifying Domain/LocalAdminGroups
4. **Update Configuration** - Modify parameters as needed without changing SSM documents
5. **Environment Changes** - Use environment-specific parameters for different environments

---

## Additional Resources

- [AWS Systems Manager Parameter Store Documentation](https://docs.aws.amazon.com/systems-manager/latest/userguide/systems-manager-parameter-store.html)
- [SSM Parameter Store Pricing](https://aws.amazon.com/systems-manager/pricing/)
- [Parameter Store Best Practices](https://docs.aws.amazon.com/systems-manager/latest/userguide/parameter-store-best-practices.html)

