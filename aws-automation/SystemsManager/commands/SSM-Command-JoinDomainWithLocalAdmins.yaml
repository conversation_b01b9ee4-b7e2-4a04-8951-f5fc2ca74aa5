---
schemaVersion: "2.2"
description: "Execute Join-DomainWithLocalAdmins.ps1 script from S3 to join EC2 instance to domain and configure local administrators"
parameters:
  Domain:
    type: String
    description: "(Required) The FQDN of the Active Directory domain to join"
    allowedPattern: '^([a-zA-Z0-9]+[\.-])+([a-zA-Z0-9])+$'

  SecretsManagerSecretArn:
    type: String
    description: "(Required) ARN of the Secrets Manager secret containing domain join credentials"
    allowedPattern: "^arn:aws:secretsmanager:[a-z0-9-]+:[0-9]{12}:secret:.+$"

  LocalAdminGroups:
    type: String
    description: "(Optional) Comma-separated list of AD groups to add as local administrators. Leave empty to use SSM Parameter Store value"
    default: ""

  LocalAdminGroupsParameterName:
    type: String
    description: "(Optional) SSM Parameter Store name containing local admin groups (e.g., /domain-join/local-admin-groups)"
    default: "/domain-join/local-admin-groups"

  ComputerName:
    type: String
    description: "(Optional) New computer name to set during domain join. Leave empty to keep current name"
    default: ""

  TargetOU:
    type: String
    description: "(Optional) Distinguished Name of the target OU for the computer object (e.g., OU=Servers,OU=AWS,DC=domain,DC=com)"
    default: ""

  Region:
    type: String
    description: "(Optional) AWS Region for Secrets Manager access"
    default: "af-south-1"
    allowedValues:
      - "af-south-1"
      - "us-east-1"
      - "us-west-2"
      - "eu-west-1"
      - "eu-central-1"

  RestartAfterJoin:
    type: String
    description: "(Optional) Restart the instance after domain join (true/false)"
    default: "true"
    allowedValues:
      - "true"
      - "false"

  S3BucketName:
    type: String
    description: "(Optional) S3 bucket containing the Join-DomainWithLocalAdmins.ps1 script"
    default: "sgt-imagebuilder"

  S3ScriptPath:
    type: String
    description: "(Optional) S3 path to the Join-DomainWithLocalAdmins.ps1 script"
    default: "windows/scripts/Join-DomainWithLocalAdmins.ps1"

mainSteps:
  - action: aws:runPowerShellScript
    name: DownloadAndExecuteDomainJoinScript
    inputs:
      runCommand:
        - |
          # SSM Command: Join Domain with Local Administrators
          # Downloads and executes Join-DomainWithLocalAdmins.ps1 from S3

          $ErrorActionPreference = "Stop"

          # Parameters from SSM Document
          $domainParam = "{{ Domain }}"
          $secretArn = "{{ SecretsManagerSecretArn }}"
          $localAdminGroupsParam = "{{ LocalAdminGroups }}"
          $localAdminGroupsParameterName = "{{ LocalAdminGroupsParameterName }}"
          $computerName = "{{ ComputerName }}"
          $targetOU = "{{ TargetOU }}"
          $region = "{{ Region }}"
          $restartAfterJoin = "{{ RestartAfterJoin }}"
          $s3Bucket = "{{ S3BucketName }}"
          $s3ScriptPath = "{{ S3ScriptPath }}"

          Write-Host "========================================="
          Write-Host "Domain Join with Local Administrators"
          Write-Host "========================================="

          # Retrieve values from SSM Parameter Store if not provided directly
          try {
              # Get Domain from Parameter Store if not provided
              if ([string]::IsNullOrWhiteSpace($domainParam)) {
                  Write-Host "Domain not provided, retrieving from SSM Parameter Store..."
                  $domainParamValue = Get-SSMParameter -Name "/domain-join/domain" -Region $region -ErrorAction Stop
                  $domain = $domainParamValue.Value
                  Write-Host "Domain from Parameter Store: $domain"
              } else {
                  $domain = $domainParam
                  Write-Host "Domain (provided): $domain"
              }

              # Get Local Admin Groups from Parameter Store if not provided
              if ([string]::IsNullOrWhiteSpace($localAdminGroupsParam)) {
                  Write-Host "Local Admin Groups not provided, retrieving from SSM Parameter Store..."
                  $adminGroupsParamValue = Get-SSMParameter -Name $localAdminGroupsParameterName -Region $region -ErrorAction Stop
                  $localAdminGroupsParam = $adminGroupsParamValue.Value
                  Write-Host "Local Admin Groups from Parameter Store: $localAdminGroupsParam"
              } else {
                  Write-Host "Local Admin Groups (provided): $localAdminGroupsParam"
              }

          } catch {
              Write-Warning "Failed to retrieve parameter from SSM Parameter Store: $($_.Exception.Message)"
              Write-Warning "Continuing with provided values..."
          }

          Write-Host "Secret ARN: $secretArn"
          Write-Host "Region: $region"
          Write-Host "S3 Bucket: $s3Bucket"
          Write-Host "S3 Script Path: $s3ScriptPath"
          Write-Host "========================================="

          try {
              # Create temp directory for script download
              $tempDir = "C:\Temp\SSM-DomainJoin"
              if (!(Test-Path $tempDir)) {
                  New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
                  Write-Host "Created temp directory: $tempDir"
              }
              
              # Download script from S3
              $scriptPath = Join-Path $tempDir "Join-DomainWithLocalAdmins.ps1"
              $s3Uri = "s3://$s3Bucket/$s3ScriptPath"
              
              Write-Host "Downloading script from S3..."
              Write-Host "Source: $s3Uri"
              Write-Host "Destination: $scriptPath"
              
              # Use AWS CLI to download the script
              $downloadResult = aws s3 cp $s3Uri $scriptPath --region $region 2>&1
              
              if ($LASTEXITCODE -ne 0) {
                  throw "Failed to download script from S3: $downloadResult"
              }
              
              if (!(Test-Path $scriptPath)) {
                  throw "Script file not found after download: $scriptPath"
              }
              
              $scriptSize = (Get-Item $scriptPath).Length
              Write-Host "Script downloaded successfully ($scriptSize bytes)"
              
              # Parse LocalAdminGroups parameter (comma-separated string to array)
              $localAdminGroups = @()
              if ($localAdminGroupsParam -and $localAdminGroupsParam -ne "") {
                  $localAdminGroups = $localAdminGroupsParam -split ',' | ForEach-Object { $_.Trim() } | Where-Object { $_ -ne "" }
                  Write-Host "Local Admin Groups: $($localAdminGroups -join ', ')"
              } else {
                  Write-Warning "No local admin groups specified"
              }
              
              # Build script parameters
              $scriptParams = @{
                  Domain = $domain
                  SecretsManagerSecretArn = $secretArn
                  LocalAdminGroups = $localAdminGroups
                  Region = $region
                  RestartAfterJoin = ($restartAfterJoin -eq 'true')
                  OutputJson = $false
              }
              
              # Add optional parameters if provided
              if ($computerName -and $computerName -ne "") {
                  $scriptParams.ComputerName = $computerName
                  Write-Host "Computer Name: $computerName"
              }
              
              if ($targetOU -and $targetOU -ne "") {
                  $scriptParams.TargetOU = $targetOU
                  Write-Host "Target OU: $targetOU"
              }
              
              Write-Host ""
              Write-Host "Executing domain join script..."
              Write-Host "========================================="
              
              # Execute the script
              $result = & $scriptPath @scriptParams
              
              if ($LASTEXITCODE -eq 0) {
                  Write-Host "========================================="
                  Write-Host "Domain join script completed successfully"
                  Write-Host "========================================="
                  
                  # Display result if available
                  if ($result) {
                      Write-Host "Result:"
                      $result | Format-List
                  }
                  
                  # Check if restart is scheduled
                  if ($restartAfterJoin -eq 'true' -and $result.RestartScheduled) {
                      Write-Host ""
                      Write-Host "IMPORTANT: System restart is scheduled in 60 seconds"
                      Write-Host "The instance will reboot to complete domain join"
                  }
                  
              } else {
                  throw "Domain join script failed with exit code: $LASTEXITCODE"
              }
              
              # Cleanup (optional - keep for troubleshooting)
              # Remove-Item $scriptPath -Force -ErrorAction SilentlyContinue
              Write-Host ""
              Write-Host "Script file retained for troubleshooting: $scriptPath"
              
          } catch {
              Write-Host "=========================================" -ForegroundColor Red
              Write-Host "ERROR: Domain join failed" -ForegroundColor Red
              Write-Host "=========================================" -ForegroundColor Red
              Write-Host "Error Message: $($_.Exception.Message)" -ForegroundColor Red
              Write-Host "Error Line: $($_.InvocationInfo.ScriptLineNumber)" -ForegroundColor Red
              Write-Host ""
              Write-Host "Troubleshooting Tips:" -ForegroundColor Yellow
              Write-Host "1. Verify the S3 bucket and script path are correct" -ForegroundColor Yellow
              Write-Host "2. Ensure the instance has IAM permissions to:" -ForegroundColor Yellow
              Write-Host "   - Read from S3 bucket: $s3Bucket" -ForegroundColor Yellow
              Write-Host "   - Access Secrets Manager secret: $secretArn" -ForegroundColor Yellow
              Write-Host "3. Check that the secret contains required fields:" -ForegroundColor Yellow
              Write-Host "   - domainJoinUserName" -ForegroundColor Yellow
              Write-Host "   - domainJoinPassword" -ForegroundColor Yellow
              Write-Host "   - domainName (optional)" -ForegroundColor Yellow
              Write-Host "4. Verify network connectivity to domain controllers" -ForegroundColor Yellow
              Write-Host "5. Check Security Groups allow AD traffic (TCP/UDP 389, 636, 88, 53, 445, 135, 3268-3269)" -ForegroundColor Yellow
              
              exit 1
          }
