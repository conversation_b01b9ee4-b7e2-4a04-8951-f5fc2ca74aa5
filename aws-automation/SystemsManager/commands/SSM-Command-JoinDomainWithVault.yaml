---
schemaVersion: "2.2"
description: "Join EC2 instance to domain using HashiCorp Vault for credentials and configure local administrators"
parameters:
  Domain:
    type: String
    description: "(Required) The FQDN of the Active Directory domain to join"
    allowedPattern: '^([a-zA-Z0-9]+[\.-])+([a-zA-Z0-9])+$'

  VaultAddress:
    type: String
    description: "(Required) HashiCorp Vault server address (e.g., https://vault.company.com:8200)"
    allowedPattern: "^https?://[a-zA-Z0-9.-]+:[0-9]+/?$"

  VaultSecretPath:
    type: String
    description: "(Required) Vault secret path containing domain join credentials (e.g., secret/domain-join/credentials)"
    allowedPattern: "^[a-zA-Z0-9/_-]+$"

  VaultAuthMethod:
    type: String
    description: "(Required) Vault authentication method"
    default: "aws"
    allowedValues:
      - "aws"
      - "token"
      - "userpass"

  VaultRole:
    type: String
    description: "(Optional) Vault role for AWS auth method"
    default: "domain-join-role"

  VaultToken:
    type: String
    description: "(Optional) Vault token for token auth method - use SSM SecureString parameter"
    default: ""

  LocalAdminGroups:
    type: String
    description: "(Optional) Comma-separated list of AD groups to add as local administrators"
    default: ""

  LocalAdminGroupsParameterName:
    type: String
    description: "(Optional) SSM Parameter Store name containing local admin groups"
    default: "/domain-join/local-admin-groups"

  ComputerName:
    type: String
    description: "(Optional) New computer name to set during domain join"
    default: ""

  RestartAfterJoin:
    type: String
    description: "(Optional) Restart the instance after domain join (true/false)"
    default: "true"
    allowedValues:
      - "true"
      - "false"

  VaultSkipTLSVerify:
    type: String
    description: "(Optional) Skip TLS verification for Vault connection (not recommended for production)"
    default: "false"
    allowedValues:
      - "true"
      - "false"

mainSteps:
  - action: aws:runPowerShellScript
    name: JoinDomainWithVault
    inputs:
      runCommand:
        - |
          # SSM Command: Join Domain with HashiCorp Vault
          # Joins EC2 instance to domain using Vault for credential management

          $ErrorActionPreference = "Stop"

          # Parameters from SSM Document
          $domain = "{{ Domain }}"
          $vaultAddress = "{{ VaultAddress }}"
          $vaultSecretPath = "{{ VaultSecretPath }}"
          $vaultAuthMethod = "{{ VaultAuthMethod }}"
          $vaultRole = "{{ VaultRole }}"
          $vaultToken = "{{ VaultToken }}"
          $localAdminGroupsParam = "{{ LocalAdminGroups }}"
          $localAdminGroupsParameterName = "{{ LocalAdminGroupsParameterName }}"
          $computerName = "{{ ComputerName }}"
          $restartAfterJoin = "{{ RestartAfterJoin }}"
          $vaultSkipTLS = "{{ VaultSkipTLSVerify }}"

          # Function to write structured log messages
          function Write-LogMessage {
              param(
                  [string]$Message,
                  [ValidateSet("INFO", "WARN", "ERROR", "SUCCESS")]
                  [string]$Level = "INFO"
              )
              
              $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
              $logMessage = "[$timestamp] [$Level] $Message"
              
              switch ($Level) {
                  "INFO" { Write-Host $logMessage -ForegroundColor Cyan }
                  "WARN" { Write-Host $logMessage -ForegroundColor Yellow }
                  "ERROR" { Write-Host $logMessage -ForegroundColor Red }
                  "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
              }
          }

          # Function to authenticate with Vault
          function Get-VaultToken {
              param(
                  [string]$VaultAddr,
                  [string]$AuthMethod,
                  [string]$Role,
                  [string]$Token,
                  [bool]$SkipTLS
              )
              
              try {
                  Write-LogMessage "Authenticating with Vault using $AuthMethod method" "INFO"
                  
                  # Set Vault environment variables
                  $env:VAULT_ADDR = $VaultAddr
                  if ($SkipTLS) {
                      $env:VAULT_SKIP_VERIFY = "true"
                      Write-LogMessage "WARNING: TLS verification disabled for Vault" "WARN"
                  }
                  
                  switch ($AuthMethod) {
                      "aws" {
                          Write-LogMessage "Using AWS IAM authentication with role: $Role" "INFO"
                          $authResult = vault auth -method=aws role=$Role 2>&1
                          if ($LASTEXITCODE -ne 0) {
                              throw "AWS auth failed: $authResult"
                          }
                          Write-LogMessage "AWS authentication successful" "SUCCESS"
                          return $null # Token is automatically set by vault CLI
                      }
                      
                      "token" {
                          if ([string]::IsNullOrWhiteSpace($Token)) {
                              throw "Token is required for token authentication method"
                          }
                          Write-LogMessage "Using token authentication" "INFO"
                          $env:VAULT_TOKEN = $Token
                          
                          # Verify token
                          $tokenInfo = vault token lookup 2>&1
                          if ($LASTEXITCODE -ne 0) {
                              throw "Token verification failed: $tokenInfo"
                          }
                          Write-LogMessage "Token authentication successful" "SUCCESS"
                          return $Token
                      }
                      
                      default {
                          throw "Unsupported authentication method: $AuthMethod"
                      }
                  }
                  
              } catch {
                  Write-LogMessage "Vault authentication failed: $($_.Exception.Message)" "ERROR"
                  throw
              }
          }

          # Function to get domain credentials from Vault
          function Get-DomainCredentialsFromVault {
              param(
                  [string]$SecretPath
              )
              
              try {
                  Write-LogMessage "Retrieving domain credentials from Vault path: $SecretPath" "INFO"
                  
                  # Read secret from Vault
                  $secretJson = vault kv get -format=json $SecretPath 2>&1
                  if ($LASTEXITCODE -ne 0) {
                      throw "Failed to read secret from Vault: $secretJson"
                  }
                  
                  $secret = $secretJson | ConvertFrom-Json
                  $secretData = $secret.data.data
                  
                  # Validate required fields
                  if (-not $secretData.domainJoinUserName) {
                      throw "domainJoinUserName not found in Vault secret"
                  }
                  
                  if (-not $secretData.domainJoinPassword) {
                      throw "domainJoinPassword not found in Vault secret"
                  }
                  
                  # Create credential object
                  $securePassword = $secretData.domainJoinPassword | ConvertTo-SecureString -AsPlainText -Force
                  $credential = New-Object System.Management.Automation.PSCredential($secretData.domainJoinUserName, $securePassword)
                  
                  Write-LogMessage "Successfully retrieved domain credentials from Vault" "SUCCESS"
                  return @{
                      Credential = $credential
                      Username = $secretData.domainJoinUserName
                      DomainName = if ($secretData.domainName) { $secretData.domainName } else { $domain }
                  }
                  
              } catch {
                  Write-LogMessage "Failed to retrieve domain credentials from Vault: $($_.Exception.Message)" "ERROR"
                  throw
              }
          }

          # Function to check current domain membership
          function Test-DomainMembership {
              try {
                  Write-LogMessage "Checking current domain membership" "INFO"
                  
                  $computerSystem = Get-WmiObject -Class Win32_ComputerSystem
                  
                  if ($computerSystem.PartOfDomain) {
                      Write-LogMessage "Computer is already domain-joined to: $($computerSystem.Domain)" "INFO"
                      return @{
                          IsDomainJoined = $true
                          CurrentDomain = $computerSystem.Domain
                          ComputerName = $computerSystem.Name
                      }
                  } else {
                      Write-LogMessage "Computer is not domain-joined (workgroup: $($computerSystem.Workgroup))" "INFO"
                      return @{
                          IsDomainJoined = $false
                          CurrentWorkgroup = $computerSystem.Workgroup
                          ComputerName = $computerSystem.Name
                      }
                  }
                  
              } catch {
                  Write-LogMessage "Failed to check domain membership: $($_.Exception.Message)" "ERROR"
                  throw
              }
          }

          # Function to join domain
          function Join-DomainSafe {
              param(
                  [string]$DomainName,
                  [System.Management.Automation.PSCredential]$Credential,
                  [string]$ComputerName
              )
              
              try {
                  Write-LogMessage "Joining domain: $DomainName" "INFO"
                  
                  # Build join parameters
                  $joinParams = @{
                      DomainName = $DomainName
                      Credential = $Credential
                      Force = $true
                  }
                  
                  # Add computer name if specified
                  if ($ComputerName -and $ComputerName -ne "") {
                      $joinParams.NewName = $ComputerName
                      Write-LogMessage "Setting computer name to: $ComputerName" "INFO"
                  }
                  
                  # Perform domain join
                  Add-Computer @joinParams -ErrorAction Stop
                  
                  Write-LogMessage "Successfully joined domain: $DomainName" "SUCCESS"
                  return $true
                  
              } catch {
                  Write-LogMessage "Failed to join domain: $($_.Exception.Message)" "ERROR"
                  throw
              }
          }

          # Function to add local administrator groups
          function Add-LocalAdministrators {
              param(
                  [array]$AdminGroups
              )
              
              try {
                  Write-LogMessage "Adding local administrator groups" "INFO"
                  
                  $addedGroups = @()
                  $skippedGroups = @()
                  $failedGroups = @()
                  
                  foreach ($group in $AdminGroups) {
                      try {
                          Write-LogMessage "Processing admin group: $group" "INFO"
                          
                          # Check if group already exists in local administrators
                          $localAdmins = Get-LocalGroupMember -Group "Administrators" -ErrorAction SilentlyContinue
                          $existingMember = $localAdmins | Where-Object { $_.Name -eq $group -or $_.Name -like "*\$group" }
                          
                          if ($existingMember) {
                              Write-LogMessage "Group '$group' is already a local administrator" "INFO"
                              $skippedGroups += $group
                          } else {
                              # Add group to local administrators
                              Add-LocalGroupMember -Group "Administrators" -Member $group -ErrorAction Stop
                              Write-LogMessage "Successfully added '$group' to local administrators" "SUCCESS"
                              $addedGroups += $group
                          }
                          
                      } catch {
                          Write-LogMessage "Failed to add group '$group' to local administrators: $($_.Exception.Message)" "WARN"
                          $failedGroups += @{
                              Group = $group
                              Error = $_.Exception.Message
                          }
                      }
                  }
                  
                  return @{
                      AddedGroups = $addedGroups
                      SkippedGroups = $skippedGroups
                      FailedGroups = $failedGroups
                      TotalProcessed = $AdminGroups.Count
                  }
                  
              } catch {
                  Write-LogMessage "Failed to process local administrator groups: $($_.Exception.Message)" "ERROR"
                  throw
              }
          }

          Write-Host "========================================="
          Write-Host "Domain Join with HashiCorp Vault"
          Write-Host "========================================="
          Write-LogMessage "Domain: $domain" "INFO"
          Write-LogMessage "Vault Address: $vaultAddress" "INFO"
          Write-LogMessage "Vault Secret Path: $vaultSecretPath" "INFO"
          Write-LogMessage "Vault Auth Method: $vaultAuthMethod" "INFO"
          Write-Host "========================================="

          try {
              # Step 1: Check if Vault CLI is available
              Write-LogMessage "Checking HashiCorp Vault CLI availability" "INFO"
              $vaultVersion = vault version 2>&1
              if ($LASTEXITCODE -ne 0) {
                  throw "HashiCorp Vault CLI not found. Ensure Vault is installed and in PATH."
              }
              Write-LogMessage "Vault CLI found: $vaultVersion" "SUCCESS"

              # Step 2: Get Local Admin Groups from Parameter Store if not provided
              $localAdminGroups = @()
              if ([string]::IsNullOrWhiteSpace($localAdminGroupsParam)) {
                  try {
                      Write-LogMessage "Local Admin Groups not provided, retrieving from SSM Parameter Store..." "INFO"
                      $adminGroupsParamValue = Get-SSMParameter -Name $localAdminGroupsParameterName -ErrorAction Stop
                      $localAdminGroupsParam = $adminGroupsParamValue.Value
                      Write-LogMessage "Local Admin Groups from Parameter Store: $localAdminGroupsParam" "INFO"
                  } catch {
                      Write-LogMessage "Failed to retrieve parameter from SSM Parameter Store: $($_.Exception.Message)" "WARN"
                      Write-LogMessage "Continuing without local admin groups..." "WARN"
                  }
              }

              # Parse LocalAdminGroups parameter
              if ($localAdminGroupsParam -and $localAdminGroupsParam -ne "") {
                  $localAdminGroups = $localAdminGroupsParam -split ',' | ForEach-Object { $_.Trim() } | Where-Object { $_ -ne "" }
                  Write-LogMessage "Local Admin Groups: $($localAdminGroups -join ', ')" "INFO"
              } else {
                  Write-LogMessage "No local admin groups specified" "WARN"
              }

              # Step 3: Authenticate with Vault
              $vaultTokenResult = Get-VaultToken -VaultAddr $vaultAddress -AuthMethod $vaultAuthMethod -Role $vaultRole -Token $vaultToken -SkipTLS ($vaultSkipTLS -eq 'true')

              # Step 4: Check current domain membership
              $currentStatus = Test-DomainMembership

              # Step 5: Get domain credentials from Vault
              $domainCreds = Get-DomainCredentialsFromVault -SecretPath $vaultSecretPath

              # Use domain from Vault secret if available
              $effectiveDomain = if ($domainCreds.DomainName) { $domainCreds.DomainName } else { $domain }

              # Step 6: Determine if domain join is needed
              $needsDomainJoin = $false
              if (-not $currentStatus.IsDomainJoined) {
                  $needsDomainJoin = $true
                  Write-LogMessage "Computer is not domain-joined - domain join required" "INFO"
              } elseif ($currentStatus.CurrentDomain -ne $effectiveDomain) {
                  $needsDomainJoin = $true
                  Write-LogMessage "Computer is joined to different domain ($($currentStatus.CurrentDomain)) - rejoin required" "WARN"
              } else {
                  Write-LogMessage "Computer is already joined to correct domain: $effectiveDomain" "SUCCESS"
              }

              # Step 7: Perform domain join if needed
              $domainJoinResult = @{
                  WasRequired = $needsDomainJoin
                  Success = $false
                  PreviousDomain = $currentStatus.CurrentDomain
                  NewDomain = $effectiveDomain
              }

              if ($needsDomainJoin) {
                  try {
                      Join-DomainSafe -DomainName $effectiveDomain -Credential $domainCreds.Credential -ComputerName $computerName
                      $domainJoinResult.Success = $true
                  } catch {
                      $domainJoinResult.Error = $_.Exception.Message
                      throw "Domain join failed: $($_.Exception.Message)"
                  }
              } else {
                  $domainJoinResult.Success = $true
              }

              # Step 8: Configure local administrators
              $adminResult = @{
                  AddedGroups = @()
                  SkippedGroups = @()
                  FailedGroups = @()
                  TotalProcessed = 0
              }

              if ($localAdminGroups.Count -gt 0) {
                  $adminResult = Add-LocalAdministrators -AdminGroups $localAdminGroups
              } else {
                  Write-LogMessage "No local admin groups to process" "INFO"
              }

              # Step 9: Build final result object
              $finalResult = @{
                  Success = $true
                  Domain = $effectiveDomain
                  ComputerName = if ($computerName -and $computerName -ne "") { $computerName } else { $env:COMPUTERNAME }
                  VaultAddress = $vaultAddress
                  VaultSecretPath = $vaultSecretPath
                  VaultAuthMethod = $vaultAuthMethod

                  # Domain join details
                  DomainJoin = $domainJoinResult

                  # Local administrator details
                  LocalAdministrators = $adminResult

                  # Metadata
                  DomainUser = $domainCreds.Username
                  RestartScheduled = ($restartAfterJoin -eq 'true') -and $needsDomainJoin
                  Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
              }

              # Step 10: Schedule restart if domain join occurred and restart is requested
              if ($needsDomainJoin -and ($restartAfterJoin -eq 'true')) {
                  Write-LogMessage "Scheduling system restart in 60 seconds due to domain join" "WARN"
                  shutdown /r /t 60 /c "Restarting after domain join - AWS SSM Automation with Vault"
                  $finalResult.RestartScheduled = $true
              }

              # Output results
              Write-LogMessage "=== DOMAIN JOIN AND LOCAL ADMIN CONFIGURATION COMPLETED ===" "SUCCESS"
              Write-LogMessage "Domain: $effectiveDomain" "INFO"
              Write-LogMessage "Domain Join Required: $needsDomainJoin" "INFO"
              Write-LogMessage "Domain Join Success: $($domainJoinResult.Success)" "INFO"
              Write-LogMessage "Local Admins Added: $($adminResult.AddedGroups.Count)" "INFO"
              Write-LogMessage "Local Admins Skipped: $($adminResult.SkippedGroups.Count)" "INFO"
              Write-LogMessage "Local Admins Failed: $($adminResult.FailedGroups.Count)" "INFO"
              if ($finalResult.RestartScheduled) {
                  Write-LogMessage "System restart scheduled in 60 seconds" "WARN"
              }

              Write-Host "========================================="
              Write-Host "Domain join with Vault completed successfully"
              Write-Host "========================================="

          } catch {
              Write-Host "=========================================" -ForegroundColor Red
              Write-Host "ERROR: Domain join with Vault failed" -ForegroundColor Red
              Write-Host "=========================================" -ForegroundColor Red
              Write-Host "Error Message: $($_.Exception.Message)" -ForegroundColor Red
              Write-Host "Error Line: $($_.InvocationInfo.ScriptLineNumber)" -ForegroundColor Red
              Write-Host ""
              Write-Host "Troubleshooting Tips:" -ForegroundColor Yellow
              Write-Host "1. Verify HashiCorp Vault is installed and accessible via 'vault' command" -ForegroundColor Yellow
              Write-Host "2. Check Vault server connectivity: $vaultAddress" -ForegroundColor Yellow
              Write-Host "3. Verify Vault authentication method and credentials" -ForegroundColor Yellow
              Write-Host "4. Ensure the secret path exists in Vault: $vaultSecretPath" -ForegroundColor Yellow
              Write-Host "5. Check that the Vault secret contains required fields:" -ForegroundColor Yellow
              Write-Host "   - domainJoinUserName" -ForegroundColor Yellow
              Write-Host "   - domainJoinPassword" -ForegroundColor Yellow
              Write-Host "   - domainName (optional)" -ForegroundColor Yellow
              Write-Host "6. Verify network connectivity to domain controllers" -ForegroundColor Yellow
              Write-Host "7. Check Security Groups allow AD traffic (TCP/UDP 389, 636, 88, 53, 445, 135, 3268-3269)" -ForegroundColor Yellow
              Write-Host "8. For AWS auth method, ensure EC2 instance has proper IAM role for Vault" -ForegroundColor Yellow
              
              exit 1
          }
