<#
.SYNOPSIS
    Setup SSM Parameter Store parameters for domain join configuration

.DESCRIPTION
    This script creates SSM Parameter Store parameters to store domain join configuration
    that can be used by the SSM Command document "JoinDomainWithLocalAdmins"

.PARAMETER Region
    AWS Region where parameters will be created

.PARAMETER Domain
    FQDN of the Active Directory domain

.PARAMETER DefaultLocalAdminGroups
    Comma-separated list of default AD groups to add as local administrators

.PARAMETER TargetOU
    Default target OU for computer objects

.PARAMETER Environment
    Environment name (DEV, PPE, PRD) for environment-specific parameters

.EXAMPLE
    .\Setup-DomainJoinParameters.ps1 -Region "af-south-1" -Domain "corp.example.com" -DefaultLocalAdminGroups "CORP\IT-Admins,CORP\Server-Admins"

.NOTES
    Author: R<PERSON> van Zyl
    Date: 2025-01-06
#>

param(
    [Parameter(Mandatory=$true)]
    [string]$Region,
    
    [Parameter(Mandatory=$true)]
    [string]$Domain,
    
    [Parameter(Mandatory=$true)]
    [string]$DefaultLocalAdminGroups,
    
    [Parameter(Mandatory=$false)]
    [string]$TargetOU = "",
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("DEV", "PPE", "PRD", "ALL")]
    [string]$Environment = "ALL",
    
    [Parameter(Mandatory=$false)]
    [switch]$Overwrite = $false
)

$ErrorActionPreference = "Stop"

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "INFO" { "Cyan" }
        "SUCCESS" { "Green" }
        "WARN" { "Yellow" }
        "ERROR" { "Red" }
        default { "White" }
    }
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

function New-SSMParameterSafe {
    param(
        [string]$Name,
        [string]$Value,
        [string]$Type = "String",
        [string]$Description,
        [string]$Region,
        [bool]$Overwrite
    )
    
    try {
        # Check if parameter exists
        $existingParam = $null
        try {
            $existingParam = Get-SSMParameter -Name $Name -Region $Region -ErrorAction SilentlyContinue
        } catch {
            # Parameter doesn't exist, which is fine
        }
        
        if ($existingParam -and -not $Overwrite) {
            Write-Log "Parameter '$Name' already exists. Use -Overwrite to update it." "WARN"
            return $false
        }
        
        if ($existingParam -and $Overwrite) {
            Write-Log "Updating existing parameter: $Name" "INFO"
            Write-SSMParameter -Name $Name -Value $Value -Type $Type -Description $Description -Region $Region -Overwrite $true | Out-Null
            Write-Log "Updated parameter: $Name" "SUCCESS"
        } else {
            Write-Log "Creating new parameter: $Name" "INFO"
            Write-SSMParameter -Name $Name -Value $Value -Type $Type -Description $Description -Region $Region | Out-Null
            Write-Log "Created parameter: $Name" "SUCCESS"
        }
        
        return $true
        
    } catch {
        Write-Log "Failed to create/update parameter '$Name': $($_.Exception.Message)" "ERROR"
        return $false
    }
}

Write-Log "=========================================" "INFO"
Write-Log "SSM Parameter Store Setup for Domain Join" "INFO"
Write-Log "=========================================" "INFO"
Write-Log "Region: $Region" "INFO"
Write-Log "Domain: $Domain" "INFO"
Write-Log "Environment: $Environment" "INFO"
Write-Log "Overwrite: $Overwrite" "INFO"
Write-Log "=========================================" "INFO"

$successCount = 0
$failCount = 0

# Create base domain parameter
if (New-SSMParameterSafe -Name "/domain-join/domain" -Value $Domain -Type "String" -Description "Active Directory domain FQDN" -Region $Region -Overwrite $Overwrite) {
    $successCount++
} else {
    $failCount++
}

# Create default local admin groups parameter
if (New-SSMParameterSafe -Name "/domain-join/local-admin-groups" -Value $DefaultLocalAdminGroups -Type "StringList" -Description "Default AD groups to add as local administrators" -Region $Region -Overwrite $Overwrite) {
    $successCount++
} else {
    $failCount++
}

# Create target OU parameter if provided
if ($TargetOU) {
    if (New-SSMParameterSafe -Name "/domain-join/target-ou" -Value $TargetOU -Type "String" -Description "Default target OU for computer objects" -Region $Region -Overwrite $Overwrite) {
        $successCount++
    } else {
        $failCount++
    }
}

# Create environment-specific parameters
if ($Environment -eq "DEV" -or $Environment -eq "ALL") {
    Write-Log "" "INFO"
    Write-Log "Creating DEV environment parameters..." "INFO"
    
    if (New-SSMParameterSafe -Name "/domain-join/dev/local-admin-groups" -Value $DefaultLocalAdminGroups -Type "StringList" -Description "DEV environment - AD groups for local administrators" -Region $Region -Overwrite $Overwrite) {
        $successCount++
    } else {
        $failCount++
    }
    
    if ($TargetOU) {
        $devOU = $TargetOU -replace "OU=Servers", "OU=DEV-Servers"
        if (New-SSMParameterSafe -Name "/domain-join/dev/target-ou" -Value $devOU -Type "String" -Description "DEV environment - Target OU for computer objects" -Region $Region -Overwrite $Overwrite) {
            $successCount++
        } else {
            $failCount++
        }
    }
}

if ($Environment -eq "PPE" -or $Environment -eq "ALL") {
    Write-Log "" "INFO"
    Write-Log "Creating PPE environment parameters..." "INFO"
    
    if (New-SSMParameterSafe -Name "/domain-join/ppe/local-admin-groups" -Value $DefaultLocalAdminGroups -Type "StringList" -Description "PPE environment - AD groups for local administrators" -Region $Region -Overwrite $Overwrite) {
        $successCount++
    } else {
        $failCount++
    }
    
    if ($TargetOU) {
        $ppeOU = $TargetOU -replace "OU=Servers", "OU=PPE-Servers"
        if (New-SSMParameterSafe -Name "/domain-join/ppe/target-ou" -Value $ppeOU -Type "String" -Description "PPE environment - Target OU for computer objects" -Region $Region -Overwrite $Overwrite) {
            $successCount++
        } else {
            $failCount++
        }
    }
}

if ($Environment -eq "PRD" -or $Environment -eq "ALL") {
    Write-Log "" "INFO"
    Write-Log "Creating PRD environment parameters..." "INFO"
    
    if (New-SSMParameterSafe -Name "/domain-join/prd/local-admin-groups" -Value $DefaultLocalAdminGroups -Type "StringList" -Description "PRD environment - AD groups for local administrators" -Region $Region -Overwrite $Overwrite) {
        $successCount++
    } else {
        $failCount++
    }
    
    if ($TargetOU) {
        $prdOU = $TargetOU -replace "OU=Servers", "OU=PRD-Servers"
        if (New-SSMParameterSafe -Name "/domain-join/prd/target-ou" -Value $prdOU -Type "String" -Description "PRD environment - Target OU for computer objects" -Region $Region -Overwrite $Overwrite) {
            $successCount++
        } else {
            $failCount++
        }
    }
}

Write-Log "" "INFO"
Write-Log "=========================================" "INFO"
Write-Log "Parameter Setup Complete" "SUCCESS"
Write-Log "=========================================" "INFO"
Write-Log "Successfully created/updated: $successCount parameters" "SUCCESS"
if ($failCount -gt 0) {
    Write-Log "Failed: $failCount parameters" "ERROR"
}
Write-Log "" "INFO"

# List all created parameters
Write-Log "Listing all domain-join parameters:" "INFO"
try {
    $allParams = Get-SSMParametersByPath -Path "/domain-join" -Recursive $true -Region $Region
    foreach ($param in $allParams) {
        Write-Log "  $($param.Name) = $($param.Value)" "INFO"
    }
} catch {
    Write-Log "Failed to list parameters: $($_.Exception.Message)" "WARN"
}

Write-Log "" "INFO"
Write-Log "To use these parameters in the SSM Command document:" "INFO"
Write-Log "  1. Leave the 'Domain' and 'LocalAdminGroups' parameters empty" "INFO"
Write-Log "  2. The document will automatically retrieve values from Parameter Store" "INFO"
Write-Log "  3. Or provide values directly to override Parameter Store values" "INFO"

