AWSTemplateFormatVersion: '2010-09-09'
Description: 'HashiCorp Vault S3 Synchronization Infrastructure'

Parameters:
  S3BucketName:
    Type: String
    Description: 'S3 bucket name for storing applications'
    Default: 'sgt-imagebuilder'
  
  S3Prefix:
    Type: String
    Description: 'S3 prefix for Vault files'
    Default: 'windows/applications/vault'
  
  ScheduleExpression:
    Type: String
    Description: 'CloudWatch Events schedule expression for automatic sync'
    Default: 'rate(1 day)'
  
  NotificationEmail:
    Type: String
    Description: 'Email address for sync notifications (optional)'
    Default: ''

Conditions:
  CreateNotifications: !Not [!Equals [!Ref NotificationEmail, '']]

Resources:
  # IAM Role for Lambda function
  VaultSyncLambdaRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub '${AWS::StackName}-vault-sync-lambda-role'
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: VaultSyncS3Access
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                  - s3:DeleteObject
                Resource: !Sub 'arn:aws:s3:::${S3BucketName}/${S3Prefix}/*'
              - Effect: Allow
                Action:
                  - s3:ListBucket
                Resource: !Sub 'arn:aws:s3:::${S3BucketName}'
                Condition:
                  StringLike:
                    's3:prefix': !Sub '${S3Prefix}/*'
        - PolicyName: VaultSyncSNSPublish
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - sns:Publish
                Resource: !If [CreateNotifications, !Ref VaultSyncNotificationTopic, !Ref 'AWS::NoValue']

  # Lambda function for Vault synchronization
  VaultSyncLambdaFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub '${AWS::StackName}-vault-sync'
      Runtime: python3.9
      Handler: index.lambda_handler
      Role: !GetAtt VaultSyncLambdaRole.Arn
      Timeout: 300
      MemorySize: 256
      Environment:
        Variables:
          S3_BUCKET_NAME: !Ref S3BucketName
          S3_PREFIX: !Ref S3Prefix
          AWS_REGION: !Ref AWS::Region
          SNS_TOPIC_ARN: !If [CreateNotifications, !Ref VaultSyncNotificationTopic, '']
      Code:
        ZipFile: |
          import json
          import boto3
          import requests
          import zipfile
          import tempfile
          import os
          from datetime import datetime
          import logging

          logger = logging.getLogger()
          logger.setLevel(logging.INFO)

          def lambda_handler(event, context):
              # Placeholder - replace with actual code from lambda-vault-sync.py
              logger.info("Vault sync function triggered")
              return {
                  'statusCode': 200,
                  'body': json.dumps({
                      'message': 'Vault sync function placeholder - update with actual code',
                      'timestamp': datetime.utcnow().isoformat()
                  })
              }

  # CloudWatch Events rule for scheduled execution
  VaultSyncScheduleRule:
    Type: AWS::Events::Rule
    Properties:
      Name: !Sub '${AWS::StackName}-vault-sync-schedule'
      Description: 'Scheduled execution of Vault sync Lambda'
      ScheduleExpression: !Ref ScheduleExpression
      State: ENABLED
      Targets:
        - Arn: !GetAtt VaultSyncLambdaFunction.Arn
          Id: VaultSyncLambdaTarget
          Input: '{"source": "scheduled", "force_update": false}'

  # Permission for CloudWatch Events to invoke Lambda
  VaultSyncLambdaInvokePermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref VaultSyncLambdaFunction
      Action: lambda:InvokeFunction
      Principal: events.amazonaws.com
      SourceArn: !GetAtt VaultSyncScheduleRule.Arn

  # SNS Topic for notifications (conditional)
  VaultSyncNotificationTopic:
    Type: AWS::SNS::Topic
    Condition: CreateNotifications
    Properties:
      TopicName: !Sub '${AWS::StackName}-vault-sync-notifications'
      DisplayName: 'HashiCorp Vault Sync Notifications'

  # SNS Subscription for email notifications (conditional)
  VaultSyncEmailSubscription:
    Type: AWS::SNS::Subscription
    Condition: CreateNotifications
    Properties:
      TopicArn: !Ref VaultSyncNotificationTopic
      Protocol: email
      Endpoint: !Ref NotificationEmail

  # CloudWatch Log Group for Lambda function
  VaultSyncLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/lambda/${AWS::StackName}-vault-sync'
      RetentionInDays: 30

  # CloudWatch Alarm for Lambda function errors
  VaultSyncErrorAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${AWS::StackName}-vault-sync-errors'
      AlarmDescription: 'Alarm for Vault sync Lambda function errors'
      MetricName: Errors
      Namespace: AWS/Lambda
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 1
      Threshold: 1
      ComparisonOperator: GreaterThanOrEqualToThreshold
      Dimensions:
        - Name: FunctionName
          Value: !Ref VaultSyncLambdaFunction
      AlarmActions:
        - !If [CreateNotifications, !Ref VaultSyncNotificationTopic, !Ref 'AWS::NoValue']

  # CloudWatch Alarm for Lambda function duration
  VaultSyncDurationAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${AWS::StackName}-vault-sync-duration'
      AlarmDescription: 'Alarm for Vault sync Lambda function long duration'
      MetricName: Duration
      Namespace: AWS/Lambda
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 240000  # 4 minutes (function timeout is 5 minutes)
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: FunctionName
          Value: !Ref VaultSyncLambdaFunction
      AlarmActions:
        - !If [CreateNotifications, !Ref VaultSyncNotificationTopic, !Ref 'AWS::NoValue']

Outputs:
  LambdaFunctionName:
    Description: 'Name of the Vault sync Lambda function'
    Value: !Ref VaultSyncLambdaFunction
    Export:
      Name: !Sub '${AWS::StackName}-lambda-function-name'

  LambdaFunctionArn:
    Description: 'ARN of the Vault sync Lambda function'
    Value: !GetAtt VaultSyncLambdaFunction.Arn
    Export:
      Name: !Sub '${AWS::StackName}-lambda-function-arn'

  ScheduleRuleName:
    Description: 'Name of the CloudWatch Events rule'
    Value: !Ref VaultSyncScheduleRule
    Export:
      Name: !Sub '${AWS::StackName}-schedule-rule-name'

  SNSTopicArn:
    Description: 'ARN of the SNS notification topic'
    Value: !If [CreateNotifications, !Ref VaultSyncNotificationTopic, 'Not created']
    Export:
      Name: !Sub '${AWS::StackName}-sns-topic-arn'

  S3Location:
    Description: 'S3 location where Vault executable will be stored'
    Value: !Sub 's3://${S3BucketName}/${S3Prefix}/vault.exe'

  ManualInvokeCommand:
    Description: 'AWS CLI command to manually invoke the sync function'
    Value: !Sub 'aws lambda invoke --function-name ${VaultSyncLambdaFunction} --payload "{\"force_update\": true}" response.json'
